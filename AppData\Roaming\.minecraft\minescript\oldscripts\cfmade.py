import requests
import minescript
from minescript import EventQueue, EventType
import time
from queue import Empty  # Import the Empty exception

def on_coinflip_message(message):
    # Check if the message contains all required keywords for a coinflip
    if all(keyword in message for keyword in ["(/cf)", "made a", "Coinflip", "for:", "GC"]):
        parts = message.split(" ")
        try:
            # Extract rank, name, and GC amount
            rank_name = parts[2].strip("[]")  # Extract "[rank]"
            name = parts[3]  # Extract "name"
            amount_str = parts[-2]  # Extract GC amount (may contain "k" for thousands)

            # Handle "k" in amount, e.g., "1k" to 1000
            if amount_str.lower().endswith("k"):
                amount = int(amount_str[:-1]) * 1000
            else:
                amount = int(amount_str)
                
        except (ValueError, IndexError):
            minescript.echo("Error parsing message for rank, name, or amount.")
            return  # Exit if parsing fails

        # Send to Discord only if the amount is 25 or greater
        if amount >= 25:
            formatted_message = f"🪙 **{name}** made a CF for **{amount}** GC <@&1283550625875103876>"
            send_to_discord(formatted_message)

def send_to_discord(message):
    webhook_url = "https://discord.com/api/webhooks/1283152937010335921/iVy802hligbyKb-yCfXhNhRI7K2b2jlQtlwIlQkVfuHegX4tsC3JXM_ILS433mBi371b"
    data = {"content": message}
    # Post the message to the Discord webhook
    response = requests.post(webhook_url, json=data)
    if response.status_code != 204:
        minescript.echo(f"Failed to send message to Discord: {response.status_code}")

# Continuous loop to monitor chat messages for coinflip events
def start_event_listener():
    while True:
        with EventQueue() as event_queue:
            event_queue.register_chat_listener()  # Register the chat listener
            try:
                # Attempt to get the next event with a timeout, handling Empty silently
                event = event_queue.get(timeout=1)
                if event and event.type == EventType.CHAT:  # Process only chat events
                    on_coinflip_message(event.message)  # Check for coinflip message and process
            except Empty:
                # Silently handle Empty, allowing the loop to continue
                pass
            except Exception as e:
                # Enhanced error message for other types of exceptions
                minescript.echo(f"Error retrieving event: {e.__class__.__name__}: {e}")
            time.sleep(0.1)  # Short delay to prevent overwhelming the loop

# Start the event listener
start_event_listener()
