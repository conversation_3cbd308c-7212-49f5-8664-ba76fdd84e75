#!/usr/bin/env python3
"""
Automated Mining Bot for Minescript
===================================

An advanced mining automation script that moves the player in a clockwise square pattern
while continuously mining. Features a modern GUI with real-time statistics and controls.

Usage: \\miner

Features:
- Automated square pattern mining in a defined area (-39,-39 to 40,40)
- Continuous left-click mining
- Real-time position tracking and statistics
- Modern CustomTkinter GUI with start/stop/pause controls
- Safety features and emergency stop
- Configurable movement timing

Author: Minescript Automation
Version: 1.0
"""

import time
import threading
import sys
import random
import json
import os
from datetime import datetime
from queue import Queue

# Try to import CustomTkinter, install if not available
try:
    import customtkinter as ctk
except ImportError:
    import subprocess
    
    print("CustomTkinter not found. Installing...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "customtkinter"])
    import customtkinter as ctk

# Try to import audio module
try:
    import winsound  # Windows
    AUDIO_AVAILABLE = "winsound"
except ImportError:
    try:
        import playsound  # Cross-platform alternative
        AUDIO_AVAILABLE = "playsound"
    except ImportError:
        AUDIO_AVAILABLE = None

# Import Minescript functions - Updated for latest API
try:
    import minescript
    from minescript import (
        player_press_forward,
        player_press_backward,
        player_press_left,
        player_press_right,
        player_press_attack,
        player_position,
        player_set_orientation,
        player_orientation,
        echo,
        execute
    )
except ImportError as e:
    print(f"Error importing minescript: {e}")
    sys.exit(1)

# Set CustomTkinter appearance
ctk.set_appearance_mode("System")
ctk.set_default_color_theme("blue")

# Profile-based configuration system
PROFILES = {
    "circle_sacred": {
        "name": "Circle Sacred",
        "mining_area": {
            "min_x": -39,
            "min_z": -39,
            "max_x": 40,
            "max_z": 40
        },
        "movement_speed": 0.001,
        "position_check_interval": 0.02,
        "gui_update_interval": 0.5,
        "safety_margin": 5,
        "mining_type": "square",
        "home_command": "/home gen2"
    },
    "line_sacred": {
        "name": "Line Sacred",
        "mining_line": {
            "x": 15,
            "y": 59,
            "min_z": -13,
            "max_z": 43
        },
        "movement_speed": 0.001,
        "position_check_interval": 0.02,
        "gui_update_interval": 0.5,
        "tolerance": 1.5,  # Increased tolerance for position precision (allows normal movement variation)
        "safety_margin": 5,  # Add safety_margin for compatibility
        "mining_type": "linear",
        "home_command": "/home gen2"
    },
    "circle_arc": {
        "name": "Circle Arc",
        "mining_area": {
            "min_x": -39,
            "min_z": -39,
            "max_x": 40,
            "max_z": 40
        },
        "movement_speed": 0.001,
        "position_check_interval": 0.02,
        "gui_update_interval": 0.5,
        "safety_margin": 5,
        "mining_type": "circle_arc",
        "home_command": "/home gen3",
        "look_points": [
            {"area": (-39, 40), "yaw": 105.5, "pitch": -57.5, "tolerance": 5},   # Top-left corner
            {"area": (40, 40), "yaw": 12.6, "pitch": -58.2, "tolerance": 5},     # Top-right corner
            {"area": (40, -39), "yaw": -81.3, "pitch": -58.6, "tolerance": 5},   # Bottom-right corner
            {"area": (-39, -39), "yaw": -167, "pitch": -59.2, "tolerance": 5}    # Bottom-left corner
        ]
    }
}

# Current active profile
CURRENT_PROFILE = "circle_sacred"  # Default to Circle Sacred profile

def get_config():
    """Get the current profile configuration."""
    return PROFILES[CURRENT_PROFILE]

# Legacy CONFIG for backward compatibility
CONFIG = get_config()

# Global state
STATE = {
    "running": False,
    "paused": False,
    "start_time": None,
    "current_position": (0, 0, 0),
    "current_direction": "forward_right",
    "emergency_stop": False,
    "mining_thread": None,
    "gui_thread": None,
    "starting_position": None,
    # Delay system removed for instant direction changes
    "last_position": None,  # Last recorded position for movement detection
    "last_movement_time": None,  # Last time player moved significantly
    "movement_check_count": 0,  # Counter for consecutive stuck checks
    "beeping_active": False,  # Whether alarm beeping is active
    "beeping_thread": None,  # Thread for beeping sound
    "pause_state_locked": False,  # Prevent pause spam
    "debug_queue": Queue(),  # Queue for thread-safe debug messages

    # Random movement variation for Line Sacred
    "last_random_movement": 0,  # Last time random movement was applied
    "random_movement_active": False,  # Whether random movement is currently active
    "random_movement_end_time": 0,  # When to stop current wiggle
    "wiggle_pattern_active": False,  # Whether a full wiggle pattern is active
    "wiggle_pattern_end_time": 0,  # When to end the entire wiggle pattern
    "current_wiggle_direction": "left",  # Current wiggle direction (left/right)
    "next_wiggle_time": 0,  # When to start the next wiggle in the pattern

    # Random yaw wiggling for Line Sacred
    "last_yaw_wiggle": 0,  # Last time yaw wiggle was applied
    "yaw_wiggle_active": False,  # Whether yaw wiggling is currently active
    "yaw_wiggle_end_time": 0,  # When to stop current yaw position
    "yaw_wiggle_pattern_active": False,  # Whether the entire yaw pattern is active
    "yaw_wiggle_pattern_end_time": 0,  # When to end the entire yaw pattern
    "current_yaw_position": -1,  # Current yaw position (-1 or 1)
    "next_yaw_change_time": 0,  # When to change to the next yaw position
    "base_yaw": None,  # Base yaw to return to after wiggling
}

class MiningDirection:
    """Enum-like class for mining directions"""
    # Square mining directions
    FORWARD_RIGHT = "forward_right"
    FORWARD_LEFT = "forward_left"
    BACKWARD_LEFT = "backward_left"
    BACKWARD_RIGHT = "backward_right"

    # Linear mining directions
    FORWARD = "forward"
    BACKWARD = "backward"

    # Linear mining with random left-right variation
    FORWARD_LEFT_WIGGLE = "forward_left_wiggle"
    FORWARD_RIGHT_WIGGLE = "forward_right_wiggle"
    BACKWARD_LEFT_WIGGLE = "backward_left_wiggle"
    BACKWARD_RIGHT_WIGGLE = "backward_right_wiggle"

def get_mining_direction(x: float, z: float) -> str:
    """
    Determine mining direction based on current position within the mining area.

    Movement pattern (clockwise square starting from top-left):
    - Starting at (-39,40): move forward + right toward (40,40)
    - Right edge (40,40) to (40,-39): move forward + left
    - Bottom edge (40,-39) to (-39,-39): move backward + left
    - Left edge (-39,-39) to (-39,40): move backward + right

    Args:
        x: Current X coordinate
        z: Current Z coordinate

    Returns:
        Direction string indicating which way to move
    """
    min_x, min_z = CONFIG["mining_area"]["min_x"], CONFIG["mining_area"]["min_z"]
    max_x, max_z = CONFIG["mining_area"]["max_x"], CONFIG["mining_area"]["max_z"]

    # Define edge tolerance for determining which edge we're on
    edge_tolerance = 1.5

    # Debug output removed for cleaner operation

    # Clockwise movement starting from top-left (-39, 40)

    # Check corners first for priority
    # Top-left corner (-39, 40) - start/restart the clockwise pattern
    if abs(x - min_x) <= edge_tolerance and abs(z - max_z) <= edge_tolerance:
        return MiningDirection.FORWARD_RIGHT

    # Top-right corner (40, 40) - transition to right edge
    elif abs(x - max_x) <= edge_tolerance and abs(z - max_z) <= edge_tolerance:
        return MiningDirection.FORWARD_LEFT

    # Bottom-right corner (40, -39) - transition to bottom edge
    elif abs(x - max_x) <= edge_tolerance and abs(z - min_z) <= edge_tolerance:
        return MiningDirection.BACKWARD_LEFT

    # Bottom-left corner (-39, -39) - transition to left edge
    elif abs(x - min_x) <= edge_tolerance and abs(z - min_z) <= edge_tolerance:
        return MiningDirection.BACKWARD_RIGHT

    # Top edge: Z is near max_z, moving right (positive X direction)
    # This includes the starting position (-39, 40)
    elif abs(z - max_z) <= edge_tolerance and min_x <= x < max_x:
        return MiningDirection.FORWARD_RIGHT

    # Right edge: X is near max_x, moving down (negative Z direction)
    elif abs(x - max_x) <= edge_tolerance and min_z <= z <= max_z:
        return MiningDirection.FORWARD_LEFT

    # Bottom edge: Z is near min_z, moving left (negative X direction)
    elif abs(z - min_z) <= edge_tolerance and min_x <= x <= max_x:
        return MiningDirection.BACKWARD_LEFT

    # Left edge: X is near min_x, moving up (positive Z direction)
    elif abs(x - min_x) <= edge_tolerance and min_z <= z <= max_z:
        return MiningDirection.BACKWARD_RIGHT

    # If we're in the middle of the area, determine closest edge
    else:
        # Calculate distances to each edge
        dist_to_top = abs(z - max_z)      # Top edge (start here)
        dist_to_right = abs(x - max_x)    # Right edge
        dist_to_bottom = abs(z - min_z)   # Bottom edge
        dist_to_left = abs(x - min_x)     # Left edge

        min_dist = min(dist_to_top, dist_to_right, dist_to_bottom, dist_to_left)

        if min_dist == dist_to_top:
            return MiningDirection.FORWARD_RIGHT
        elif min_dist == dist_to_right:
            return MiningDirection.FORWARD_LEFT
        elif min_dist == dist_to_bottom:
            return MiningDirection.BACKWARD_LEFT
        else:  # closest to left edge
            return MiningDirection.BACKWARD_RIGHT

def get_linear_mining_direction(x: float, y: float, z: float) -> str:
    """
    Determine linear mining direction for Line Sacred profile.

    Movement pattern:
    - Move from Z=-13 to Z=43 (forward movement)
    - Turn around and move from Z=43 to Z=-13 (backward movement)
    - Stay at X=15, Y=59 throughout
    - Instant direction changes with no delays
    - Occasional random left-right wiggle for human-like behavior

    Args:
        x: Current X coordinate (used for boundary checking)
        y: Current Y coordinate (used for boundary checking)
        z: Current Z coordinate

    Returns:
        Direction string for linear movement
    """
    import random
    import time

    config = get_config()
    min_z = config["mining_line"]["min_z"]
    max_z = config["mining_line"]["max_z"]
    tolerance = config["tolerance"]
    current_time = time.time()

    # Determine base direction (forward or backward)
    if z <= min_z + tolerance:
        # At or near the minimum Z, move forward (positive Z direction) - instant
        base_direction = "forward"
    elif z >= max_z - tolerance:
        # At or near the maximum Z, move backward (negative Z direction) - instant
        base_direction = "backward"
    else:
        # In the middle - continue current direction or default to forward
        current_dir = STATE.get("current_direction", MiningDirection.FORWARD)
        if "backward" in current_dir.lower():
            base_direction = "backward"
        else:
            base_direction = "forward"

    # Check if we should start a new wiggle pattern (5% chance every 2 minutes)
    if (current_time - STATE["last_random_movement"] >= 120.0 and  # 2 minutes = 120 seconds
        not STATE["wiggle_pattern_active"] and
        random.random() < 0.05):  # 5% chance

        STATE["wiggle_pattern_active"] = True
        STATE["wiggle_pattern_end_time"] = current_time + random.uniform(4.0, 8.0)  # 4-8 second pattern
        STATE["last_random_movement"] = current_time
        STATE["current_wiggle_direction"] = "left"  # Start with left
        STATE["random_movement_active"] = True
        STATE["random_movement_end_time"] = current_time + random.uniform(0.5, 1.0)  # First wiggle duration
        STATE["next_wiggle_time"] = 0  # Will be set after first wiggle ends
        debug_log("§d[Miner] Starting wiggle pattern: left → wait → right → wait → repeat")

    # Handle wiggle pattern timing
    if STATE["wiggle_pattern_active"]:
        # Check if current wiggle should end
        if STATE["random_movement_active"] and current_time >= STATE["random_movement_end_time"]:
            STATE["random_movement_active"] = False
            # Set next wiggle time (1 second delay between wiggles)
            STATE["next_wiggle_time"] = current_time + 1.0
            debug_log(f"§d[Miner] Ending {STATE['current_wiggle_direction']} wiggle, waiting 1 second")

        # Check if it's time to start the next wiggle
        if (not STATE["random_movement_active"] and
            STATE["next_wiggle_time"] > 0 and
            current_time >= STATE["next_wiggle_time"]):

            # Switch wiggle direction
            STATE["current_wiggle_direction"] = "right" if STATE["current_wiggle_direction"] == "left" else "left"
            STATE["random_movement_active"] = True
            STATE["random_movement_end_time"] = current_time + random.uniform(0.5, 1.0)  # Wiggle duration
            STATE["next_wiggle_time"] = 0  # Reset
            debug_log(f"§d[Miner] Starting {STATE['current_wiggle_direction']} wiggle")

        # Check if entire wiggle pattern should end
        if current_time >= STATE["wiggle_pattern_end_time"]:
            STATE["wiggle_pattern_active"] = False
            STATE["random_movement_active"] = False
            debug_log("§d[Miner] Ending wiggle pattern")

    # Check if we should start yaw wiggle pattern (5% chance every 2 minutes, separate from movement wiggle)
    if (current_time - STATE["last_yaw_wiggle"] >= 120.0 and  # 2 minutes = 120 seconds
        not STATE["yaw_wiggle_pattern_active"] and
        random.random() < 0.05):  # 5% chance

        STATE["yaw_wiggle_pattern_active"] = True
        STATE["yaw_wiggle_pattern_end_time"] = current_time + random.uniform(8.0, 12.0)  # 8-12 minute pattern
        STATE["last_yaw_wiggle"] = current_time
        STATE["current_yaw_position"] = -1  # Start with -1
        STATE["yaw_wiggle_active"] = True
        STATE["next_yaw_change_time"] = current_time + random.uniform(60.0, 120.0)  # 1-2 minutes until next change

        # Store base yaw to return to
        try:
            current_orientation = player_orientation()
            STATE["base_yaw"] = current_orientation[0]  # yaw is first element
            debug_log(f"§d[Miner] Starting yaw wiggle pattern: base yaw {STATE['base_yaw']:.1f}, starting at -1 degree")
        except Exception as e:
            debug_log(f"§c[Miner] Error getting orientation for yaw wiggle: {e}")
            STATE["yaw_wiggle_pattern_active"] = False

    # Handle yaw wiggle pattern timing
    if STATE["yaw_wiggle_pattern_active"]:
        # Check if it's time to change yaw position
        if current_time >= STATE["next_yaw_change_time"]:
            # Switch yaw position
            STATE["current_yaw_position"] = 1 if STATE["current_yaw_position"] == -1 else -1
            STATE["next_yaw_change_time"] = current_time + random.uniform(60.0, 120.0)  # 1-2 minutes until next change
            debug_log(f"§d[Miner] Changing yaw position to {STATE['current_yaw_position']} degree")

        # Check if entire yaw pattern should end
        if current_time >= STATE["yaw_wiggle_pattern_end_time"]:
            STATE["yaw_wiggle_pattern_active"] = False
            STATE["yaw_wiggle_active"] = False
            debug_log("§d[Miner] Ending yaw wiggle pattern")



    # Apply random left-right movement if active
    if STATE["random_movement_active"] and STATE["wiggle_pattern_active"]:
        # Use the current wiggle direction
        if STATE["current_wiggle_direction"] == "left":
            if base_direction == "forward":
                return MiningDirection.FORWARD_LEFT_WIGGLE
            else:
                return MiningDirection.BACKWARD_LEFT_WIGGLE
        else:  # right
            if base_direction == "forward":
                return MiningDirection.FORWARD_RIGHT_WIGGLE
            else:
                return MiningDirection.BACKWARD_RIGHT_WIGGLE

    # Normal movement without wiggle
    if base_direction == "forward":
        return MiningDirection.FORWARD
    else:
        return MiningDirection.BACKWARD

def get_circle_arc_direction(x: float, z: float) -> str:
    """
    Determine Circle Arc mining direction - only moves right around the circle.
    Also handles precise yaw/pitch changes at specific corner coordinates.

    Args:
        x: Current X coordinate
        z: Current Z coordinate

    Returns:
        Always returns "right" for Circle Arc movement
    """
    config = get_config()

    # Track which corner we're currently targeting (if any)
    current_target = None
    closest_distance = float('inf')

    # Find the closest corner we're within tolerance of
    for i, look_point in enumerate(config["look_points"]):
        target_x, target_z = look_point["area"]
        tolerance = look_point["tolerance"]

        # Calculate distance to this corner
        distance = ((x - target_x) ** 2 + (z - target_z) ** 2) ** 0.5

        # Check if we're within tolerance and this is the closest corner
        if distance <= tolerance and distance < closest_distance:
            current_target = look_point
            closest_distance = distance
            corner_name = ["Top-left", "Top-right", "Bottom-right", "Bottom-left"][i]
            current_target["name"] = corner_name

    # If we're near a corner, handle orientation changes
    if current_target is not None:
        target_yaw = current_target["yaw"]
        target_pitch = current_target["pitch"]
        corner_name = current_target["name"]

        # Check timing - wait 0.3-0.5 seconds before starting orientation changes
        import time
        import random
        current_time = time.time()

        # Initialize timing tracking for this corner if not exists
        corner_key = f"{current_target['area'][0]}_{current_target['area'][1]}"
        if not hasattr(get_circle_arc_direction, '_corner_entry_times'):
            get_circle_arc_direction._corner_entry_times = {}
        if not hasattr(get_circle_arc_direction, '_corner_delays'):
            get_circle_arc_direction._corner_delays = {}

        # Track when we first entered this corner's range and set random delay
        if corner_key not in get_circle_arc_direction._corner_entry_times:
            get_circle_arc_direction._corner_entry_times[corner_key] = current_time
            get_circle_arc_direction._corner_delays[corner_key] = random.uniform(0.3, 0.5)
            delay = get_circle_arc_direction._corner_delays[corner_key]
            debug_log(f"§d[Circle Arc] {corner_name}: Entered corner range, waiting {delay:.2f}s before orientation change...")
            return "right"  # Continue moving right while waiting

        # Check if the random delay has passed since entering this corner
        delay = get_circle_arc_direction._corner_delays[corner_key]
        time_in_corner = current_time - get_circle_arc_direction._corner_entry_times[corner_key]
        if time_in_corner < delay:
            remaining_time = delay - time_in_corner
            debug_log(f"§e[Circle Arc] {corner_name}: Waiting {remaining_time:.2f}s before orientation change...")
            return "right"  # Continue moving right while waiting

        try:
            # Get current orientation
            current_yaw, current_pitch = player_orientation()

            # Calculate differences
            yaw_diff = target_yaw - current_yaw
            pitch_diff = target_pitch - current_pitch

            # Handle yaw wrapping (shortest path around circle)
            if yaw_diff > 180:
                yaw_diff -= 360
            elif yaw_diff < -180:
                yaw_diff += 360

            # Calculate total angular distance
            total_angular_distance = (yaw_diff ** 2 + pitch_diff ** 2) ** 0.5

            # Detailed debug logging
            debug_log(f"§d[Circle Arc] {corner_name} corner ({current_target['area'][0]}, {current_target['area'][1]})")
            debug_log(f"§d[Circle Arc] Position: ({x:.1f}, {z:.1f}), Distance: {closest_distance:.1f}")
            debug_log(f"§d[Circle Arc] Current: yaw={current_yaw:.1f}°, pitch={current_pitch:.1f}°")
            debug_log(f"§d[Circle Arc] Target: yaw={target_yaw:.1f}°, pitch={target_pitch:.1f}°")
            debug_log(f"§d[Circle Arc] Differences: yaw_diff={yaw_diff:.1f}°, pitch_diff={pitch_diff:.1f}°")
            debug_log(f"§d[Circle Arc] Total angular distance: {total_angular_distance:.1f}°")

            # Determine if we need to adjust orientation
            precision_threshold = 0.5  # Within 0.5 degrees is considered "at target"

            if total_angular_distance <= precision_threshold:
                # We're very close - ensure we're exactly at target
                if abs(yaw_diff) > 0.1 or abs(pitch_diff) > 0.1:
                    success = player_set_orientation(target_yaw, target_pitch)
                    debug_log(f"§a[Circle Arc] {corner_name}: Precision snap to exact target - Success: {success}")
                else:
                    debug_log(f"§a[Circle Arc] {corner_name}: Already at target (within 0.1°)")
            elif total_angular_distance <= 5.0:
                # We're close - use aggressive transition
                smooth_factor = 0.5  # 50% per update for fast convergence
                new_yaw = current_yaw + (yaw_diff * smooth_factor)
                new_pitch = current_pitch + (pitch_diff * smooth_factor)

                success = player_set_orientation(new_yaw, new_pitch)
                debug_log(f"§e[Circle Arc] {corner_name}: Fast transition -> yaw={new_yaw:.1f}°, pitch={new_pitch:.1f}° - Success: {success}")
            else:
                # We're far - use moderate transition
                smooth_factor = 0.3  # 30% per update
                new_yaw = current_yaw + (yaw_diff * smooth_factor)
                new_pitch = current_pitch + (pitch_diff * smooth_factor)

                success = player_set_orientation(new_yaw, new_pitch)
                debug_log(f"§6[Circle Arc] {corner_name}: Smooth transition -> yaw={new_yaw:.1f}°, pitch={new_pitch:.1f}° - Success: {success}")

            # Verify the orientation was actually set
            if 'success' in locals():
                if success:
                    # Double-check by reading back the orientation
                    verify_yaw, verify_pitch = player_orientation()
                    debug_log(f"§b[Circle Arc] Verification: yaw={verify_yaw:.1f}°, pitch={verify_pitch:.1f}°")
                else:
                    debug_log(f"§c[Circle Arc] ERROR: player_set_orientation() returned False!")

        except Exception as e:
            debug_log(f"§c[Circle Arc] Exception in orientation handling: {e}")
            import traceback
            debug_log(f"§c[Circle Arc] Traceback: {traceback.format_exc()}")
    else:
        # Not near any corner - clean up timing data and log position occasionally
        import time

        # Clean up corner entry times and delays when not near any corner
        if hasattr(get_circle_arc_direction, '_corner_entry_times'):
            get_circle_arc_direction._corner_entry_times.clear()
        if hasattr(get_circle_arc_direction, '_corner_delays'):
            get_circle_arc_direction._corner_delays.clear()

        if not hasattr(get_circle_arc_direction, '_last_log_time'):
            get_circle_arc_direction._last_log_time = 0

        current_time = time.time()
        if current_time - get_circle_arc_direction._last_log_time > 5.0:  # Log every 5 seconds
            debug_log(f"§7[Circle Arc] Moving right at ({x:.1f}, {z:.1f}) - No corner in range")
            get_circle_arc_direction._last_log_time = current_time

    # Always return right movement for Circle Arc
    return "right"

def is_position_safe(x: float, z: float) -> bool:
    """
    Check if current position is within safe mining bounds.
    Supports both square and linear mining profiles.

    Args:
        x: Current X coordinate
        z: Current Z coordinate

    Returns:
        True if position is safe, False if outside bounds
    """
    config = get_config()

    if config["mining_type"] == "linear":
        # For linear mining, use the mining line bounds with tolerance
        expected_x = config["mining_line"]["x"]
        min_z = config["mining_line"]["min_z"]
        max_z = config["mining_line"]["max_z"]
        tolerance = config["tolerance"]
        margin = config["safety_margin"]

        # Check X coordinate with margin
        if abs(x - expected_x) > tolerance + margin:
            return False

        # Check Z coordinate with margin
        if z < min_z - margin or z > max_z + margin:
            return False

        return True
    else:
        # For square mining, use the mining area bounds
        margin = config["safety_margin"]
        min_x = config["mining_area"]["min_x"] - margin
        min_z = config["mining_area"]["min_z"] - margin
        max_x = config["mining_area"]["max_x"] + margin
        max_z = config["mining_area"]["max_z"] + margin

        return min_x <= x <= max_x and min_z <= z <= max_z

def check_boundary_failsafe(x: float, y: float, z: float) -> bool:
    """
    Enhanced boundary check that verifies player is within defined mining area.
    Triggers emergency stop if player is outside the boundary box.
    Supports both square and linear mining profiles.

    Args:
        x: X coordinate
        y: Y coordinate (height level)
        z: Z coordinate

    Returns:
        True if within bounds, False if outside (emergency stop triggered)
    """
    config = get_config()

    if config["mining_type"] == "linear":
        # For linear mining, use the Line Sacred boundary check
        return check_line_sacred_boundary(x, y, z)
    else:
        # For square and circle_arc mining, use the original boundary check
        min_x = config["mining_area"]["min_x"]
        max_x = config["mining_area"]["max_x"]
        min_z = config["mining_area"]["min_z"]
        max_z = config["mining_area"]["max_z"]
        expected_y = 178  # Expected mining level

        # Add small tolerance for movement precision
        xy_tolerance = 1.0  # 1 block buffer for X/Z
        y_tolerance = 3.0   # 3 block tolerance for Y (allows for jumping/falling)

        min_x_safe = min_x - xy_tolerance
        max_x_safe = max_x + xy_tolerance
        min_z_safe = min_z - xy_tolerance
        max_z_safe = max_z + xy_tolerance
        min_y_safe = expected_y - y_tolerance
        max_y_safe = expected_y + y_tolerance

        # Check X and Z boundaries
        xy_in_bounds = (min_x_safe <= x <= max_x_safe and min_z_safe <= z <= max_z_safe)

        # Check Y boundary
        y_in_bounds = (min_y_safe <= y <= max_y_safe)

        if not xy_in_bounds:
            echo(f"§c[FAILSAFE] Player outside XZ mining area - Emergency stop activated!")
            echo(f"§c[FAILSAFE] Position: ({x:.1f}, {y:.1f}, {z:.1f}) - Safe XZ Area: ({min_x_safe},{min_z_safe}) to ({max_x_safe},{max_z_safe})")
            STATE["emergency_stop"] = True
            stop_all_movements()
            start_beeping()
            return False

        if not y_in_bounds:
            echo(f"§c[FAILSAFE] Player outside Y mining level - Emergency stop activated!")
            echo(f"§c[FAILSAFE] Position: ({x:.1f}, {y:.1f}, {z:.1f}) - Expected Y: {expected_y} (±{y_tolerance})")
            STATE["emergency_stop"] = True
            stop_all_movements()
            start_beeping()
            return False

        return True

def check_line_sacred_boundary(x: float, y: float, z: float) -> bool:
    """
    Enhanced boundary check for Line Sacred profile.
    Ensures player stays exactly on the mining line.

    Args:
        x: X coordinate
        y: Y coordinate
        z: Z coordinate

    Returns:
        True if within bounds, False if outside (emergency stop triggered)
    """
    config = get_config()
    expected_x = config["mining_line"]["x"]
    expected_y = config["mining_line"]["y"]
    min_z = config["mining_line"]["min_z"]
    max_z = config["mining_line"]["max_z"]
    tolerance = config["tolerance"]

    # Check X coordinate (must stay at X=15)
    if abs(x - expected_x) > tolerance:
        echo(f"§c[FAILSAFE] Player off X-axis - Emergency stop activated!")
        echo(f"§c[FAILSAFE] Position: ({x:.1f}, {y:.1f}, {z:.1f}) - Expected X: {expected_x} (±{tolerance})")
        STATE["emergency_stop"] = True
        stop_all_movements()
        start_beeping()
        return False

    # Check Y coordinate (must stay at Y=59)
    if abs(y - expected_y) > tolerance:
        echo(f"§c[FAILSAFE] Player off Y-axis - Emergency stop activated!")
        echo(f"§c[FAILSAFE] Position: ({x:.1f}, {y:.1f}, {z:.1f}) - Expected Y: {expected_y} (±{tolerance})")
        STATE["emergency_stop"] = True
        stop_all_movements()
        start_beeping()
        return False

    # Check Z coordinate (must stay within mining line)
    if z < min_z - tolerance or z > max_z + tolerance:
        echo(f"§c[FAILSAFE] Player outside Z mining line - Emergency stop activated!")
        echo(f"§c[FAILSAFE] Position: ({x:.1f}, {y:.1f}, {z:.1f}) - Z Range: {min_z} to {max_z} (±{tolerance})")
        STATE["emergency_stop"] = True
        stop_all_movements()
        start_beeping()
        return False

    return True

def stop_all_movements():
    """Stop all player movements and actions safely."""
    try:
        player_press_forward(False)
        player_press_backward(False)
        player_press_left(False)
        player_press_right(False)
        player_press_attack(False)
        echo("§c[Miner] All movements stopped")
    except Exception as e:
        print(f"Error stopping movements: {e}")

def apply_movement_direction(direction: str, force_apply: bool = False):
    """
    Apply the specified movement direction with optimized key transitions.
    Only changes keys that need to be changed for smoother movement.

    Args:
        direction: Direction string from MiningDirection
        force_apply: If True, applies all keys regardless of current state
    """
    current_dir = STATE["current_direction"]

    # Define what keys each direction uses
    direction_keys = {
        # Square mining directions
        MiningDirection.FORWARD_RIGHT: {"forward": True, "backward": False, "left": False, "right": True},
        MiningDirection.FORWARD_LEFT: {"forward": True, "backward": False, "left": True, "right": False},
        MiningDirection.BACKWARD_LEFT: {"forward": False, "backward": True, "left": True, "right": False},
        MiningDirection.BACKWARD_RIGHT: {"forward": False, "backward": True, "left": False, "right": True},

        # Linear mining directions
        MiningDirection.FORWARD: {"forward": True, "backward": False, "left": False, "right": False},
        MiningDirection.BACKWARD: {"forward": False, "backward": True, "left": False, "right": False},

        # Linear mining with random left-right variation
        MiningDirection.FORWARD_LEFT_WIGGLE: {"forward": True, "backward": False, "left": True, "right": False},
        MiningDirection.FORWARD_RIGHT_WIGGLE: {"forward": True, "backward": False, "left": False, "right": True},
        MiningDirection.BACKWARD_LEFT_WIGGLE: {"forward": False, "backward": True, "left": True, "right": False},
        MiningDirection.BACKWARD_RIGHT_WIGGLE: {"forward": False, "backward": True, "left": False, "right": True},

        # Circle Arc direction (only right movement)
        "right": {"forward": False, "backward": False, "left": False, "right": True},
    }

    # Get current and new key states
    current_keys = direction_keys.get(current_dir, {"forward": False, "backward": False, "left": False, "right": False})
    new_keys = direction_keys.get(direction, {"forward": False, "backward": False, "left": False, "right": False})

    # Apply movement keys based on direction with proper sequencing
    # For Line Sacred profile, ensure conflicting keys are released first
    config = get_config()
    if config["mining_type"] == "linear":
        # For linear movement, ensure forward/backward conflict resolution
        if current_keys["forward"] and new_keys["backward"]:
            # Switching from forward to backward - release forward first, then immediately apply backward
            player_press_forward(False)
            player_press_backward(True)
            debug_log("§d[Miner] Instant forward→backward transition")
            return  # Skip normal key application since we handled it
        elif current_keys["backward"] and new_keys["forward"]:
            # Switching from backward to forward - release backward first, then immediately apply forward
            player_press_backward(False)
            player_press_forward(True)
            debug_log("§d[Miner] Instant backward→forward transition")
            return  # Skip normal key application since we handled it

    # Apply movement keys
    if force_apply or current_keys["forward"] != new_keys["forward"]:
        player_press_forward(new_keys["forward"])

    if force_apply or current_keys["backward"] != new_keys["backward"]:
        player_press_backward(new_keys["backward"])

    if force_apply or current_keys["left"] != new_keys["left"]:
        player_press_left(new_keys["left"])

    if force_apply or current_keys["right"] != new_keys["right"]:
        player_press_right(new_keys["right"])

    # Always keep attacking while mining
    player_press_attack(True)

def apply_yaw_wiggle():
    """
    Apply yaw wiggling for Line Sacred profile only.
    Alternates between -1 and +1 degrees from base position with 1-2 minute intervals.
    """
    config = get_config()

    # Only apply yaw wiggling for Line Sacred profile
    if config["mining_type"] != "linear" or not STATE["yaw_wiggle_active"] or STATE["base_yaw"] is None:
        return

    try:
        # Get current orientation to preserve pitch
        current_orientation = player_orientation()
        current_pitch = current_orientation[1]

        # Calculate yaw position: base_yaw + current position (-1 or +1)
        new_yaw = STATE["base_yaw"] + STATE["current_yaw_position"]

        # Set new orientation (fixed yaw position, same pitch)
        player_set_orientation(new_yaw, current_pitch)

    except Exception as e:
        debug_log(f"§c[Miner] Error applying yaw wiggle: {e}")



def play_beeping_sound():
    """Play continuous beeping sound in a separate thread."""
    if AUDIO_AVAILABLE == "winsound":
        import winsound
        while STATE["beeping_active"]:
            try:
                winsound.Beep(1000, 500)  # 1000Hz for 500ms
                time.sleep(0.3)  # 300ms pause
            except:
                break
    elif AUDIO_AVAILABLE == "playsound":
        # For cross-platform, we'd need a beep sound file
        # This is a simplified implementation
        while STATE["beeping_active"]:
            try:
                print("\a")  # System beep
                time.sleep(0.8)
            except:
                break
    else:
        # Fallback to system beep
        while STATE["beeping_active"]:
            try:
                print("\a")  # System beep
                time.sleep(0.8)
            except:
                break

def start_beeping():
    """Start the beeping alarm."""
    if not STATE["beeping_active"]:
        STATE["beeping_active"] = True
        STATE["beeping_thread"] = threading.Thread(target=play_beeping_sound, daemon=True)
        STATE["beeping_thread"].start()
        echo("§c[AUDIO] Alarm beeping started!")

def stop_beeping():
    """Stop the beeping alarm."""
    if STATE["beeping_active"]:
        STATE["beeping_active"] = False
        if STATE["beeping_thread"]:
            STATE["beeping_thread"].join(timeout=1)
        echo("§a[AUDIO] Alarm beeping stopped!")

def load_overall_stats():
    """Load overall statistics from JSON file."""
    stats_file = "miner_overall_stats.json"
    default_stats = {
        "total_runtime_seconds": 0,
        "total_sessions": 0
    }

    try:
        if os.path.exists(stats_file):
            with open(stats_file, 'r') as f:
                return json.load(f)
        else:
            return default_stats
    except Exception as e:
        echo(f"§c[STATS] Error loading stats: {e}")
        return default_stats

def save_overall_stats(session_runtime_seconds):
    """Save updated overall statistics to JSON file with improved error handling."""
    stats_file = "miner_overall_stats.json"

    try:
        current_stats = load_overall_stats()
        current_stats["total_runtime_seconds"] += session_runtime_seconds
        current_stats["total_sessions"] += 1

        # Atomic write to prevent corruption
        temp_file = stats_file + ".tmp"
        with open(temp_file, 'w') as f:
            json.dump(current_stats, f, indent=2)

        # Atomic rename
        import os
        if os.path.exists(stats_file):
            os.remove(stats_file)
        os.rename(temp_file, stats_file)

        echo(f"§a[STATS] Session saved: +{session_runtime_seconds}s runtime, {current_stats['total_sessions']} total sessions")
        debug_log(f"§a[STATS] Stats updated successfully")
    except Exception as e:
        echo(f"§c[STATS] Error saving stats: {e}")
        debug_log(f"§c[STATS] Save error details: {str(e)}")

def format_time_from_seconds(seconds):
    """Format seconds into HH:MM:SS format with improved error handling."""
    try:
        if not isinstance(seconds, (int, float)) or seconds < 0:
            return "00:00:00"

        seconds = int(seconds)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    except (TypeError, ValueError, AttributeError):
        return "00:00:00"



def debug_log(message: str):
    """
    Add a debug message to the GUI debug panel instead of Minecraft chat.
    Thread-safe function that can be called from any thread.

    Args:
        message: Debug message to display
    """
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"

    try:
        STATE["debug_queue"].put(formatted_message)
    except Exception as e:
        # Fallback to echo if queue fails
        echo(f"§c[DEBUG-ERROR] {e}: {message}")

def echo_or_debug(message: str):
    """
    Route message to either debug panel or Minecraft chat based on content.
    Debug messages (containing [DEBUG]) go to GUI, others go to chat.

    Args:
        message: Message to display
    """
    if "[DEBUG]" in message:
        debug_log(message)
    else:
        echo(message)

def mining_loop():
    """
    Main mining loop that runs in a separate thread.
    Modernized with improved error handling and performance optimizations.
    """
    config = get_config()
    profile_name = config["name"]
    echo(f"§a[Miner] Starting automated mining with profile: {profile_name}")

    try:
        # Store starting position with error handling
        try:
            start_pos = player_position()
            STATE["starting_position"] = start_pos
            echo(f"§e[Miner] Starting position: {start_pos[0]:.1f}, {start_pos[1]:.1f}, {start_pos[2]:.1f}")
        except Exception as e:
            echo(f"§c[Miner] Error getting starting position: {e}")
            return

        # Initialize timing variables
        last_position_check = time.time()
        last_movement_refresh = time.time()
        last_boundary_check = time.time()

        # Start mining immediately with error handling
        try:
            player_press_attack(True)
        except Exception as e:
            echo(f"§c[Miner] Error starting attack: {e}")
            return

        # Determine initial direction based on profile type
        try:
            if config["mining_type"] == "linear":
                initial_direction = get_linear_mining_direction(start_pos[0], start_pos[1], start_pos[2])
            elif config["mining_type"] == "circle_arc":
                initial_direction = get_circle_arc_direction(start_pos[0], start_pos[2])
            else:
                initial_direction = get_mining_direction(start_pos[0], start_pos[2])

            echo(f"§e[Miner] Initial direction detected: {initial_direction}")
            debug_log(f"§e[Miner] Initial direction detected: {initial_direction}")
        except Exception as e:
            echo(f"§c[Miner] Error determining initial direction: {e}")
            initial_direction = "forward_right"  # Fallback direction

        # Apply initial movement direction immediately
        # Set current direction to None first so the optimized logic will apply all keys
        STATE["current_direction"] = None
        apply_movement_direction(initial_direction)
        STATE["current_direction"] = initial_direction

        # Initialize movement detection
        STATE["last_position"] = start_pos
        STATE["last_movement_time"] = time.time()
        STATE["movement_check_count"] = 0

        while STATE["running"] and not STATE["emergency_stop"]:
            current_time = time.time()
            
            # Check if paused
            if STATE["paused"]:
                if not STATE["pause_state_locked"]:
                    stop_all_movements()
                    STATE["pause_state_locked"] = True
                time.sleep(0.02)  # Reduced pause delay for faster resume
                continue
            else:
                # If just resumed from pause, reapply current movement
                if STATE["pause_state_locked"]:
                    STATE["pause_state_locked"] = False
                    apply_movement_direction(STATE["current_direction"], force_apply=True)
            
            # Get current position periodically with improved error handling
            if current_time - last_position_check >= config["position_check_interval"]:
                try:
                    pos = player_position()
                    if pos is None or len(pos) < 3:
                        debug_log("§c[Miner] Invalid position data received")
                        continue

                    STATE["current_position"] = pos
                    x, y, z = pos[0], pos[1], pos[2]

                    # Debug position updates every few seconds
                    if current_time - last_position_check >= 2.0:  # Every 2 seconds
                        debug_log(f"§b[Miner] Position: ({x:.1f}, {y:.1f}, {z:.1f}) - Direction: {STATE['current_direction']}")

                    # Enhanced movement detection safeguard
                    if current_time - STATE["last_movement_time"] >= 1.0:
                        if STATE["last_position"] is not None:
                            # Calculate 3D distance moved
                            last_pos = STATE["last_position"]
                            distance_moved = ((pos[0] - last_pos[0])**2 +
                                            (pos[1] - last_pos[1])**2 +
                                            (pos[2] - last_pos[2])**2)**0.5

                            if distance_moved < 0.5:
                                STATE["movement_check_count"] += 1
                                debug_log(f"§e[Miner] Movement check {STATE['movement_check_count']}/3 - Distance: {distance_moved:.2f}")

                                if STATE["movement_check_count"] >= 3:
                                    echo("§c[SAFEGUARD] Player stuck - Emergency stop activated!")
                                    STATE["emergency_stop"] = True
                                    start_beeping()
                                    break
                            else:
                                STATE["movement_check_count"] = 0  # Reset counter if moving normally

                        STATE["last_position"] = pos
                        STATE["last_movement_time"] = current_time

                    # Enhanced safety check with profile-specific validation
                    if not is_position_safe(x, z):
                        echo(f"§c[Miner] EMERGENCY STOP: Outside safe area at {x:.1f}, {z:.1f}")
                        STATE["emergency_stop"] = True
                        start_beeping()
                        break

                    last_position_check = current_time

                except Exception as e:
                    echo(f"§c[Miner] Error getting position: {e}")
                    debug_log(f"§c[Miner] Position error details: {str(e)}")
                    time.sleep(0.1)  # Brief recovery delay
                    continue

            # Enhanced direction change logic with better error handling
            if STATE["current_position"] is not None:
                try:
                    pos = STATE["current_position"]
                    x, y, z = pos[0], pos[1], pos[2]

                    # Determine movement direction based on profile type with error handling
                    detected_direction = None
                    position_str = ""

                    if config["mining_type"] == "linear":
                        detected_direction = get_linear_mining_direction(x, y, z)
                        position_str = f"({x:.1f}, {y:.1f}, {z:.1f})"
                    elif config["mining_type"] == "circle_arc":
                        detected_direction = get_circle_arc_direction(x, z)
                        position_str = f"({x:.1f}, {z:.1f})"
                    else:
                        detected_direction = get_mining_direction(x, z)
                        position_str = f"({x:.1f}, {z:.1f})"

                    # Apply direction change immediately if different and valid
                    if detected_direction and detected_direction != STATE["current_direction"]:
                        debug_log(f"§e[Miner] Position {position_str} - Direction change: {STATE['current_direction']} → {detected_direction}")
                        STATE["current_direction"] = detected_direction
                        echo(f"§a[Miner] Direction change: {detected_direction}")
                        apply_movement_direction(detected_direction, force_apply=True)

                except Exception as e:
                    echo(f"§c[Miner] Error in direction change logic: {e}")
                    debug_log(f"§c[Miner] Direction change error details: {str(e)}")

            # Enhanced boundary failsafe check with better error handling
            if current_time - last_boundary_check >= 0.5:
                try:
                    pos = player_position()
                    if pos and len(pos) >= 3:
                        # Use appropriate boundary check based on profile
                        boundary_safe = False
                        if config["mining_type"] == "linear":
                            boundary_safe = check_line_sacred_boundary(pos[0], pos[1], pos[2])
                        else:
                            boundary_safe = check_boundary_failsafe(pos[0], pos[1], pos[2])

                        if not boundary_safe:
                            echo("§c[FAILSAFE] Boundary violation - Emergency stop!")
                            STATE["emergency_stop"] = True
                            start_beeping()
                            break
                except Exception as e:
                    echo(f"§c[FAILSAFE] Error checking boundary: {e}")
                    debug_log(f"§c[FAILSAFE] Boundary check error: {str(e)}")
                last_boundary_check = current_time

            # Periodic movement refresh with error handling
            if current_time - last_movement_refresh >= 2.0:
                try:
                    if STATE["current_direction"]:
                        apply_movement_direction(STATE["current_direction"], force_apply=True)
                        debug_log(f"§7[Miner] Movement refresh: {STATE['current_direction']}")
                except Exception as e:
                    debug_log(f"§c[Miner] Movement refresh error: {e}")
                last_movement_refresh = current_time

            # Ensure continuous attacking with error handling
            try:
                player_press_attack(True)
            except Exception as e:
                debug_log(f"§c[Miner] Attack error: {e}")

            # Apply profile-specific behaviors
            try:
                apply_yaw_wiggle()  # For Line Sacred profile only
            except Exception as e:
                debug_log(f"§c[Miner] Yaw wiggle error: {e}")

            # Optimized delay to prevent excessive CPU usage
            time.sleep(config["movement_speed"])
            
    except Exception as e:
        echo(f"§c[Miner] Error in mining loop: {e}")
    finally:
        stop_all_movements()
        echo("§c[Miner] Mining stopped")

class MinerGUI(ctk.CTk):
    """Main GUI class for the mining bot."""

    def __init__(self):
        super().__init__()

        self.title("Automated Mining Bot")
        self.geometry("450x700")
        self.resizable(True, True)

        # Configure grid weights for responsive design
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)

        self.setup_ui()
        self.setup_profile_info()
        self.update_stats_loop()

    def setup_ui(self):
        """Set up the user interface."""
        # Title
        title_label = ctk.CTkLabel(
            self,
            text="[MINER] Automated Mining Bot",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=20, sticky="ew")

        # Create tabview for different sections
        self.tabview = ctk.CTkTabview(self)
        self.tabview.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")

        # Add tabs
        self.tabview.add("Mining Control")
        self.tabview.add("Profile Info")

        # Configure tab content frames
        control_tab = self.tabview.tab("Mining Control")
        control_tab.grid_columnconfigure(0, weight=1)
        control_tab.grid_rowconfigure(0, weight=1)

        profile_tab = self.tabview.tab("Profile Info")
        profile_tab.grid_columnconfigure(0, weight=1)
        profile_tab.grid_rowconfigure(0, weight=1)

        # Main scrollable content frame for mining control
        main_frame = ctk.CTkScrollableFrame(control_tab)
        main_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)

        # Profile info scrollable frame
        self.profile_info_frame = ctk.CTkScrollableFrame(profile_tab)
        self.profile_info_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        self.profile_info_frame.grid_columnconfigure(0, weight=1)

        # Control buttons frame
        control_frame = ctk.CTkFrame(main_frame)
        control_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        control_frame.grid_columnconfigure((0, 1), weight=1)

        # Start/Stop button
        self.start_stop_btn = ctk.CTkButton(
            control_frame,
            text="Start Mining",
            command=self.toggle_mining,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40
        )
        self.start_stop_btn.grid(row=0, column=0, padx=(0, 10), pady=10, sticky="ew")

        # Pause/Resume button
        self.pause_resume_btn = ctk.CTkButton(
            control_frame,
            text="Pause",
            command=self.toggle_pause,
            font=ctk.CTkFont(size=16),
            height=40,
            state="disabled"
        )
        self.pause_resume_btn.grid(row=0, column=1, padx=(10, 0), pady=10, sticky="ew")

        # Profile selection
        profile_frame = ctk.CTkFrame(control_frame)
        profile_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0), sticky="ew")
        profile_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(profile_frame, text="Profile:", font=ctk.CTkFont(size=12, weight="bold")).grid(
            row=0, column=0, padx=(10, 5), pady=5, sticky="w"
        )

        self.profile_var = ctk.StringVar(value=CURRENT_PROFILE)
        self.profile_dropdown = ctk.CTkOptionMenu(
            profile_frame,
            variable=self.profile_var,
            values=list(PROFILES.keys()),
            command=self.change_profile,
            font=ctk.CTkFont(size=12)
        )
        self.profile_dropdown.grid(row=0, column=1, padx=(0, 10), pady=5, sticky="ew")

        # Emergency stop button
        emergency_btn = ctk.CTkButton(
            control_frame,
            text="!!! EMERGENCY STOP !!!",
            command=self.emergency_stop,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color="red",
            hover_color="darkred",
            height=35
        )
        emergency_btn.grid(row=2, column=0, columnspan=2, pady=(10, 0), sticky="ew")

        # Command buttons frame
        command_frame = ctk.CTkFrame(control_frame)
        command_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0), sticky="ew")
        command_frame.grid_columnconfigure((0, 1), weight=1)

        # Home Gen button
        home_gen_btn = ctk.CTkButton(
            command_frame,
            text="Home Gen",
            command=self.execute_home_gen,
            font=ctk.CTkFont(size=14),
            height=35
        )
        home_gen_btn.grid(row=0, column=0, padx=(0, 5), pady=5, sticky="ew")

        # Skyblock button
        skyblock_btn = ctk.CTkButton(
            command_frame,
            text="Skyblock",
            command=self.execute_skyblock,
            font=ctk.CTkFont(size=14),
            height=35
        )
        skyblock_btn.grid(row=0, column=1, padx=(5, 0), pady=5, sticky="ew")

        # Audio control buttons
        audio_frame = ctk.CTkFrame(control_frame)
        audio_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0), sticky="ew")
        audio_frame.grid_columnconfigure((0, 1), weight=1)

        # Test Beeping button
        test_beep_btn = ctk.CTkButton(
            audio_frame,
            text="Test Beeping",
            command=self.test_beeping,
            font=ctk.CTkFont(size=12),
            height=30
        )
        test_beep_btn.grid(row=0, column=0, padx=(0, 5), pady=5, sticky="ew")

        # Stop Beeping button
        stop_beep_btn = ctk.CTkButton(
            audio_frame,
            text="Stop Beeping",
            command=self.stop_beeping,
            font=ctk.CTkFont(size=12),
            height=30,
            fg_color="orange",
            hover_color="darkorange"
        )
        stop_beep_btn.grid(row=0, column=1, padx=(5, 0), pady=5, sticky="ew")

        # Status frame
        status_frame = ctk.CTkFrame(main_frame)
        status_frame.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")

        # Status indicator
        ctk.CTkLabel(status_frame, text="Status:", font=ctk.CTkFont(weight="bold")).grid(
            row=0, column=0, padx=20, pady=(15, 5), sticky="w"
        )
        self.status_label = ctk.CTkLabel(status_frame, text="Stopped", text_color="red")
        self.status_label.grid(row=0, column=1, padx=(0, 20), pady=(15, 5), sticky="w")

        # Session Statistics frame
        session_stats_frame = ctk.CTkFrame(main_frame)
        session_stats_frame.grid(row=2, column=0, padx=10, pady=(0, 10), sticky="ew")
        session_stats_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(session_stats_frame, text="[SESSION] Current Session", font=ctk.CTkFont(size=16, weight="bold")).grid(
            row=0, column=0, columnspan=2, pady=(15, 10), sticky="ew"
        )

        # Session Runtime
        ctk.CTkLabel(session_stats_frame, text="Runtime:").grid(row=1, column=0, padx=(20, 10), pady=5, sticky="w")
        self.runtime_label = ctk.CTkLabel(session_stats_frame, text="00:00:00")
        self.runtime_label.grid(row=1, column=1, padx=(0, 20), pady=5, sticky="w")



        # Current position
        ctk.CTkLabel(session_stats_frame, text="Position:").grid(row=2, column=0, padx=(20, 10), pady=5, sticky="w")
        self.position_label = ctk.CTkLabel(session_stats_frame, text="0, 0, 0")
        self.position_label.grid(row=2, column=1, padx=(0, 20), pady=5, sticky="w")

        # Current direction
        ctk.CTkLabel(session_stats_frame, text="Direction:").grid(row=3, column=0, padx=(20, 10), pady=(5, 15), sticky="w")
        self.direction_label = ctk.CTkLabel(session_stats_frame, text="None")
        self.direction_label.grid(row=3, column=1, padx=(0, 20), pady=(5, 15), sticky="w")

        # Overall Statistics frame
        overall_stats_frame = ctk.CTkFrame(main_frame)
        overall_stats_frame.grid(row=3, column=0, padx=10, pady=(0, 10), sticky="ew")
        overall_stats_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(overall_stats_frame, text="[OVERALL] All-Time Stats", font=ctk.CTkFont(size=16, weight="bold")).grid(
            row=0, column=0, columnspan=2, pady=(15, 10), sticky="ew"
        )

        # Total runtime
        ctk.CTkLabel(overall_stats_frame, text="Total Runtime:").grid(row=1, column=0, padx=(20, 10), pady=5, sticky="w")
        self.total_runtime_label = ctk.CTkLabel(overall_stats_frame, text="00:00:00")
        self.total_runtime_label.grid(row=1, column=1, padx=(0, 20), pady=5, sticky="w")

        # Total sessions
        ctk.CTkLabel(overall_stats_frame, text="Sessions:").grid(row=2, column=0, padx=(20, 10), pady=(5, 15), sticky="w")
        self.total_sessions_label = ctk.CTkLabel(overall_stats_frame, text="0")
        self.total_sessions_label.grid(row=2, column=1, padx=(0, 20), pady=(5, 15), sticky="w")

        # Load and display overall stats
        self.load_and_display_overall_stats()

        # Debug Panel
        debug_frame = ctk.CTkFrame(main_frame)
        debug_frame.grid(row=4, column=0, padx=10, pady=(0, 10), sticky="ew")
        debug_frame.grid_columnconfigure(0, weight=1)

        # Debug panel header with clear button
        debug_header_frame = ctk.CTkFrame(debug_frame)
        debug_header_frame.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="ew")
        debug_header_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(debug_header_frame, text="[DEBUG] Debug Output", font=ctk.CTkFont(size=16, weight="bold")).grid(
            row=0, column=0, padx=(10, 0), pady=5, sticky="w"
        )

        clear_debug_btn = ctk.CTkButton(
            debug_header_frame,
            text="Clear Debug Log",
            command=self.clear_debug_log,
            font=ctk.CTkFont(size=12),
            height=25,
            width=120
        )
        clear_debug_btn.grid(row=0, column=1, padx=(0, 10), pady=5, sticky="e")

        # Debug text area
        self.debug_textbox = ctk.CTkTextbox(
            debug_frame,
            height=180,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word"
        )
        self.debug_textbox.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")

        # Start debug update loop
        self.update_debug_panel()



    def toggle_mining(self):
        """Toggle mining on/off."""
        if not STATE["running"]:
            self.start_mining()
        else:
            self.stop_mining()

    def load_and_display_overall_stats(self):
        """Load and display overall statistics."""
        try:
            stats = load_overall_stats()

            self.total_runtime_label.configure(text=format_time_from_seconds(stats["total_runtime_seconds"]))
            self.total_sessions_label.configure(text=str(stats["total_sessions"]))
        except Exception as e:
            echo(f"§c[STATS] Error displaying overall stats: {e}")

    def start_mining(self):
        """Start the mining process."""
        STATE["running"] = True
        STATE["paused"] = False
        STATE["emergency_stop"] = False
        STATE["start_time"] = datetime.now()
        STATE["pause_state_locked"] = False  # Reset pause lock

        # Start mining thread
        STATE["mining_thread"] = threading.Thread(target=mining_loop, daemon=True)
        STATE["mining_thread"].start()

        # Update UI
        self.start_stop_btn.configure(text="Stop Mining", fg_color="red", hover_color="darkred")
        self.pause_resume_btn.configure(state="normal")

        echo("§a[Miner] Mining started!")
        debug_log("§a[Miner] Mining started!")

    def stop_mining(self):
        """Stop the mining process."""
        # Calculate session stats before stopping
        session_runtime = 0
        if STATE["start_time"]:
            session_runtime = (datetime.now() - STATE["start_time"]).total_seconds()

        STATE["running"] = False
        STATE["paused"] = False
        STATE["pause_state_locked"] = False

        # Wait for thread to finish
        if STATE["mining_thread"] and STATE["mining_thread"].is_alive():
            STATE["mining_thread"].join(timeout=2)

        stop_all_movements()

        # Save overall stats
        if session_runtime > 0:
            save_overall_stats(int(session_runtime))
            self.load_and_display_overall_stats()

        # Update UI
        self.start_stop_btn.configure(text="Start Mining", fg_color="#1f538d", hover_color="#14375e")
        self.pause_resume_btn.configure(text="Pause", state="disabled")

        echo("§c[Miner] Mining stopped!")
        debug_log("§c[Miner] Mining stopped!")

    def toggle_pause(self):
        """Toggle pause/resume."""
        if not STATE["paused"]:
            STATE["paused"] = True
            STATE["pause_state_locked"] = False  # Will be set to True in mining loop
            self.pause_resume_btn.configure(text="Resume")
            echo("§e[Miner] Mining paused")
            debug_log("§e[Miner] Mining paused")
        else:
            STATE["paused"] = False
            STATE["pause_state_locked"] = False
            self.pause_resume_btn.configure(text="Pause")
            echo("§a[Miner] Mining resumed - resuming movement and mining")
            debug_log("§a[Miner] Mining resumed")

    def emergency_stop(self):
        """Emergency stop all operations."""
        STATE["emergency_stop"] = True
        STATE["running"] = False
        STATE["paused"] = False
        STATE["pause_state_locked"] = False

        stop_all_movements()

        # Update UI
        self.start_stop_btn.configure(text="Start Mining", fg_color="#1f538d", hover_color="#14375e")
        self.pause_resume_btn.configure(text="Pause", state="disabled")

        echo("§c[Miner] EMERGENCY STOP ACTIVATED!")

    def test_beeping(self):
        """Test the beeping sound."""
        if not STATE["beeping_active"]:
            start_beeping()
            echo("§e[AUDIO] Test beeping started - use 'Stop Beeping' to stop")
        else:
            echo("§e[AUDIO] Beeping already active")

    def stop_beeping(self):
        """Stop the beeping sound."""
        stop_beeping()

    def change_profile(self, selected_profile):
        """Change the active mining profile."""
        global CURRENT_PROFILE, CONFIG
        if selected_profile in PROFILES:
            CURRENT_PROFILE = selected_profile
            CONFIG = get_config()  # Update CONFIG reference
            profile_name = CONFIG["name"]
            echo(f"§a[Miner] Profile changed to: {profile_name}")
            debug_log(f"§a[Miner] Profile changed to: {profile_name}")
            # Refresh the profile info display
            self.setup_profile_info()
        else:
            echo(f"§c[Miner] Invalid profile: {selected_profile}")

    def execute_home_gen(self):
        """Execute profile-specific home command."""
        try:
            config = get_config()
            home_command = config["home_command"]
            execute(home_command)
            echo(f"§a[Miner] Executed: {home_command}")
        except Exception as e:
            echo(f"§c[Miner] Error executing home command: {e}")

    def execute_skyblock(self):
        """Execute /skyblock command."""
        try:
            execute("/skyblock")
            echo("§a[Miner] Executed: /skyblock")
        except Exception as e:
            echo(f"§c[Miner] Error executing /skyblock: {e}")

    def clear_debug_log(self):
        """Clear the debug log textbox."""
        try:
            self.debug_textbox.delete("1.0", "end")
            self.debug_textbox.insert("1.0", f"[{datetime.now().strftime('%H:%M:%S')}] Debug log cleared.\n")
        except Exception as e:
            echo(f"§c[DEBUG] Error clearing debug log: {e}")

    def update_debug_panel(self):
        """Update the debug panel with new messages from the queue."""
        try:
            # Process all pending debug messages
            messages_added = 0
            while not STATE["debug_queue"].empty() and messages_added < 10:  # Limit to prevent GUI freezing
                try:
                    message = STATE["debug_queue"].get_nowait()
                    self.debug_textbox.insert("end", f"{message}\n")
                    messages_added += 1
                except:
                    break

            # Auto-scroll to bottom if messages were added
            if messages_added > 0:
                self.debug_textbox.see("end")

            # Limit textbox content to prevent memory issues (keep last 1000 lines)
            content = self.debug_textbox.get("1.0", "end")
            lines = content.split('\n')
            if len(lines) > 1000:
                # Keep last 800 lines plus a truncation notice
                truncated_content = "[DEBUG LOG TRUNCATED - Showing last 800 entries]\n" + '\n'.join(lines[-800:])
                self.debug_textbox.delete("1.0", "end")
                self.debug_textbox.insert("1.0", truncated_content)
                self.debug_textbox.see("end")

        except Exception as e:
            print(f"Error updating debug panel: {e}")

        # Schedule next update
        self.after(100, self.update_debug_panel)  # Update every 100ms

    def setup_profile_info(self):
        """Set up the comprehensive profile information display."""
        try:
            # Clear existing content
            for widget in self.profile_info_frame.winfo_children():
                widget.destroy()

            # Title
            title_label = ctk.CTkLabel(
                self.profile_info_frame,
                text="Mining Bot Profile Breakdown",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            title_label.grid(row=0, column=0, pady=(10, 20), sticky="ew")

            row = 1

            # Circle Sacred Profile
            row = self.add_profile_section(self.profile_info_frame, "Circle Sacred", "circle_sacred", row)

            # Separator
            separator = ctk.CTkFrame(self.profile_info_frame, height=2)
            separator.grid(row=row, column=0, pady=20, sticky="ew")
            row += 1

            # Line Sacred Profile
            row = self.add_profile_section(self.profile_info_frame, "Line Sacred", "line_sacred", row)

            # Separator
            separator2 = ctk.CTkFrame(self.profile_info_frame, height=2)
            separator2.grid(row=row, column=0, pady=20, sticky="ew")
            row += 1

            # Circle Arc Profile
            row = self.add_profile_section(self.profile_info_frame, "Circle Arc", "circle_arc", row)

            # Shared Features
            shared_frame = ctk.CTkFrame(self.profile_info_frame)
            shared_frame.grid(row=row, column=0, pady=(20, 10), sticky="ew")
            shared_frame.grid_columnconfigure(0, weight=1)

            shared_title = ctk.CTkLabel(
                shared_frame,
                text="🔗 Shared Features (All Three Profiles)",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color="#4CAF50"
            )
            shared_title.grid(row=0, column=0, pady=(15, 10), sticky="ew")

            shared_info = """• Movement Speed: 0.001s (1ms) main loop delay
• Position Check Interval: 0.02s (20ms)
• GUI Update Interval: 0.5s (500ms)
• Emergency Stop: Available for both profiles
• Pause/Resume: Available for both profiles
• Debug Output: Thread-safe debug panel
• Statistics Tracking: Session and overall stats
• Audio Alarms: Emergency beeping system
• Combat: Continuous attack during mining
• Performance: Identical loop timing and responsiveness"""

            shared_text = ctk.CTkLabel(
                shared_frame,
                text=shared_info,
                font=ctk.CTkFont(size=11),
                justify="left"
            )
            shared_text.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="w")

        except Exception as e:
            # Fallback error display
            error_label = ctk.CTkLabel(
                self.profile_info_frame,
                text=f"Error loading profile info: {e}",
                font=ctk.CTkFont(size=12),
                text_color="red"
            )
            error_label.grid(row=0, column=0, pady=20, sticky="ew")

    def add_profile_section(self, parent, profile_name, profile_key, start_row):
        """Add a complete profile section to the info display."""
        config = PROFILES[profile_key]

        # Profile header
        profile_frame = ctk.CTkFrame(parent)
        profile_frame.grid(row=start_row, column=0, pady=(10, 0), sticky="ew")
        profile_frame.grid_columnconfigure(0, weight=1)

        # Profile title with emoji
        if profile_key == "circle_sacred":
            emoji = "🔷"
            color = "#2196F3"
        elif profile_key == "circle_arc":
            emoji = "🎯"
            color = "#9C27B0"
        else:
            emoji = "📏"
            color = "#FF9800"

        title = ctk.CTkLabel(
            profile_frame,
            text=f"{emoji} {profile_name} Profile",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=color
        )
        title.grid(row=0, column=0, pady=(15, 10), sticky="ew")

        # Profile identification
        id_info = f"""
🏷️ PROFILE IDENTIFICATION:
• Name: "{config['name']}"
• Mining Type: {config['mining_type']}
• Home Command: {config['home_command']}
• Profile Key: {profile_key}
        """

        id_label = ctk.CTkLabel(
            profile_frame,
            text=id_info.strip(),
            font=ctk.CTkFont(size=11),
            justify="left"
        )
        id_label.grid(row=1, column=0, padx=15, pady=5, sticky="w")

        # Mining pattern details
        if config['mining_type'] == 'square':
            pattern_info = f"""
⚙️ MINING PATTERN:
• Algorithm: Clockwise square pattern movement
• Mining Area: ({config['mining_area']['min_x']},{config['mining_area']['min_z']}) to ({config['mining_area']['max_x']},{config['mining_area']['max_z']})
• Total Area: {(config['mining_area']['max_x'] - config['mining_area']['min_x'] + 1) * (config['mining_area']['max_z'] - config['mining_area']['min_z'] + 1)} blocks
• Movement: Forward+Right → Forward+Left → Backward+Left → Backward+Right
• Corner Detection: All 4 corners with priority handling
• Edge Tolerance: 1.5 blocks for determining current edge
            """
        elif config['mining_type'] == 'circle_arc':
            pattern_info = f"""
⚙️ MINING PATTERN:
• Algorithm: Circle Arc movement (right-only)
• Mining Area: ({config['mining_area']['min_x']},{config['mining_area']['min_z']}) to ({config['mining_area']['max_x']},{config['mining_area']['max_z']})
• Total Area: {(config['mining_area']['max_x'] - config['mining_area']['min_x'] + 1) * (config['mining_area']['max_z'] - config['mining_area']['min_z'] + 1)} blocks
• Movement: Continuous right movement around the circle
• Look Direction: Smooth yaw/pitch changes at specific coordinates
• Look Points: 4 strategic positions with smooth transitions
• Orientation: Dynamic camera adjustment for optimal mining angles
            """
        else:
            pattern_info = f"""
⚙️ MINING PATTERN:
• Algorithm: Linear back-and-forth movement
• Mining Line: X={config['mining_line']['x']}, Y={config['mining_line']['y']}, Z={config['mining_line']['min_z']} to {config['mining_line']['max_z']}
• Total Length: {config['mining_line']['max_z'] - config['mining_line']['min_z']} blocks
• Movement: Forward (Z+) ↔ Backward (Z-)
• Coordinate Constraints: Must stay exactly on X={config['mining_line']['x']}, Y={config['mining_line']['y']} line
• Endpoint Detection: Instant direction changes at Z boundaries
            """

        pattern_label = ctk.CTkLabel(
            profile_frame,
            text=pattern_info.strip(),
            font=ctk.CTkFont(size=11),
            justify="left"
        )
        pattern_label.grid(row=2, column=0, padx=15, pady=5, sticky="w")

        # Movement features
        movement_info = f"""
🚀 MOVEMENT FEATURES:
• Direction Changes: Instant (no delays)
• Movement Speed: {config['movement_speed']}s ({int(config['movement_speed']*1000)}ms) main loop delay
• Position Check: {config['position_check_interval']}s ({int(config['position_check_interval']*1000)}ms) interval
• Responsiveness: Maximum (instant transitions)
• Key Optimization: Only changes keys that need changing
• Movement Refresh: Every 2 seconds (force reapply all keys)
        """

        movement_label = ctk.CTkLabel(
            profile_frame,
            text=movement_info.strip(),
            font=ctk.CTkFont(size=11),
            justify="left"
        )
        movement_label.grid(row=3, column=0, padx=15, pady=5, sticky="w")

        # Human-like behavior
        if config['mining_type'] == 'linear':
            behavior_info = """
🤖 HUMAN-LIKE BEHAVIOR:
• Movement Wiggling: 5% chance every 2 minutes (120s)
  - Pattern: Left → Wait 1s → Right → Wait 1s → Repeat
  - Duration: 4-8 seconds total per session
  - Individual Wiggles: 0.5-1.0 seconds each

• Yaw Wiggling: 5% chance every 2 minutes (independent)
  - Pattern: -1° → Wait 1-2min → +1° → Wait 1-2min → Repeat
  - Duration: 8-12 minutes total per session
  - Position Changes: Every 1-2 minutes (60-120s)
            """
        elif config['mining_type'] == 'circle_arc':
            behavior_info = """
🤖 HUMAN-LIKE BEHAVIOR:
• Movement Pattern: Continuous right-only movement around circle perimeter
• Look Direction: Smooth yaw/pitch transitions at corner positions
  - Top-left (-39, 40) → Yaw: 105.5°, Pitch: -57.5°
  - Top-right (40, 40) → Yaw: 12.6°, Pitch: -58.2°
  - Bottom-right (40, -39) → Yaw: -81.3°, Pitch: -58.6°
  - Bottom-left (-39, -39) → Yaw: -167°, Pitch: -59.2°

• Timing System:
  - Entry Delay: Random 0.3-0.5 seconds before orientation changes
  - Transition Speed: Multi-tier (50% fast, 30% moderate, snap precision)
  - Precision Threshold: 0.5° for exact target achievement
  - Corner Tolerance: 5 blocks radius around each corner

• Orientation Features:
  - Yaw Wrapping: Shortest path calculation (handles 360° transitions)
  - Smooth Interpolation: Gradual transitions prevent jarring movements
  - Error Recovery: Graceful fallback if orientation changes fail
  - Verification System: Confirms successful orientation changes
            """
        else:
            behavior_info = """
🤖 HUMAN-LIKE BEHAVIOR:
• Random Movement: None (standard square pattern only)
• Yaw Wiggling: None
• Movement Variation: None
• Behavior Type: Purely algorithmic, no randomization
            """

        behavior_label = ctk.CTkLabel(
            profile_frame,
            text=behavior_info.strip(),
            font=ctk.CTkFont(size=11),
            justify="left"
        )
        behavior_label.grid(row=4, column=0, padx=15, pady=5, sticky="w")

        # Safety features
        if config['mining_type'] == 'linear':
            safety_info = f"""
🛡️ SAFETY & FAILSAFE:
• Boundary Type: 3D line enforcement (X, Y, Z coordinates)
• Position Tolerance: {config['tolerance']} blocks for all coordinates
• X Coordinate: Must be {config['mining_line']['x']} ±{config['tolerance']} ({config['mining_line']['x']-config['tolerance']} to {config['mining_line']['x']+config['tolerance']})
• Y Coordinate: Must be {config['mining_line']['y']} ±{config['tolerance']} ({config['mining_line']['y']-config['tolerance']} to {config['mining_line']['y']+config['tolerance']})
• Z Coordinate: Must be {config['mining_line']['min_z']} to {config['mining_line']['max_z']} ±{config['tolerance']}
• Emergency Triggers: Off any axis, movement stuck detection
• Check Frequency: Every 0.5 seconds
            """
        else:
            safety_info = f"""
🛡️ SAFETY & FAILSAFE:
• Boundary Type: Square area with safety margin
• Safety Margin: {config['safety_margin']} blocks outside mining area
• XY Tolerance: 1.0 block buffer for X/Z coordinates
• Y Level: 178 ±3 blocks tolerance
• Emergency Triggers: Outside XZ area, wrong Y level, movement stuck
• Check Frequency: Every 0.5 seconds
• Movement Detection: Every 1 second (0.5 block minimum movement)
            """

        safety_label = ctk.CTkLabel(
            profile_frame,
            text=safety_info.strip(),
            font=ctk.CTkFont(size=11),
            justify="left"
        )
        safety_label.grid(row=5, column=0, padx=15, pady=5, sticky="w")

        # Combat settings
        combat_info = """
⚔️ COMBAT SETTINGS:
• Attack Behavior: Continuous player_press_attack(True)
• Attack During Movement: Yes, maintained during all movements
• Attack During Direction Changes: Yes, never interrupted
• Attack During Wiggling: Yes (Line Sacred only)
• Attack Enforcement: Every main loop cycle
        """

        combat_label = ctk.CTkLabel(
            profile_frame,
            text=combat_info.strip(),
            font=ctk.CTkFont(size=11),
            justify="left"
        )
        combat_label.grid(row=6, column=0, padx=15, pady=(5, 15), sticky="w")

        return start_row + 1

    def update_stats_loop(self):
        """Update statistics display periodically."""
        try:
            # Update status
            if STATE["emergency_stop"]:
                self.status_label.configure(text="EMERGENCY STOP", text_color="red")
            elif STATE["running"]:
                if STATE["paused"]:
                    self.status_label.configure(text="Paused", text_color="orange")
                else:
                    self.status_label.configure(text="Running", text_color="green")
            else:
                self.status_label.configure(text="Stopped", text_color="red")

            # Update runtime
            if STATE["start_time"] and STATE["running"]:
                runtime = datetime.now() - STATE["start_time"]
                runtime_str = str(runtime).split('.')[0]  # Remove microseconds
                self.runtime_label.configure(text=runtime_str)
            else:
                self.runtime_label.configure(text="00:00:00")

            # Update position
            pos = STATE["current_position"]
            position_str = f"{pos[0]:.1f}, {pos[1]:.1f}, {pos[2]:.1f}"
            self.position_label.configure(text=position_str)

            # Update direction
            direction = STATE["current_direction"].replace("_", " ").title()
            self.direction_label.configure(text=direction)



            # Update overall stats periodically (every 10 updates)
            if hasattr(self, '_stats_update_counter'):
                self._stats_update_counter += 1
            else:
                self._stats_update_counter = 1

            if self._stats_update_counter % 10 == 0:
                self.load_and_display_overall_stats()

        except Exception as e:
            print(f"Error updating stats: {e}")

        # Schedule next update
        self.after(int(CONFIG["gui_update_interval"] * 1000), self.update_stats_loop)

def main():
    """
    Main function to run the modernized mining bot.
    Enhanced with better error handling and initialization.
    """
    try:
        echo("§a[Miner] Initializing Automated Mining Bot v2.0...")
        echo(f"§e[Miner] Available profiles: {', '.join(PROFILES.keys())}")
        echo(f"§e[Miner] Current profile: {CURRENT_PROFILE}")

        # Validate current profile
        if CURRENT_PROFILE not in PROFILES:
            echo(f"§c[Miner] Warning: Invalid profile '{CURRENT_PROFILE}', using default")

        # Initialize debug logging
        debug_log("§a[Miner] Debug logging initialized")
        debug_log(f"§e[Miner] Minescript API version check completed")

        # Create and run GUI with error handling
        try:
            app = MinerGUI()
            echo("§a[Miner] GUI initialized successfully")
            app.mainloop()
        except Exception as gui_error:
            echo(f"§c[Miner] GUI Error: {gui_error}")
            raise

    except KeyboardInterrupt:
        echo("§c[Miner] Interrupted by user (Ctrl+C)")
    except ImportError as import_error:
        echo(f"§c[Miner] Import Error: {import_error}")
        echo("§c[Miner] Please ensure minescript and customtkinter are properly installed")
    except Exception as e:
        echo(f"§c[Miner] Unexpected Error: {e}")
        debug_log(f"§c[Miner] Error details: {str(e)}")
        import traceback
        debug_log(f"§c[Miner] Traceback: {traceback.format_exc()}")
    finally:
        # Comprehensive cleanup
        try:
            STATE["running"] = False
            STATE["emergency_stop"] = True
            stop_all_movements()

            # Stop any active beeping
            if STATE.get("beeping_active", False):
                stop_beeping()

            echo("§c[Miner] Cleanup completed successfully")
            debug_log("§c[Miner] All systems shut down")
        except Exception as cleanup_error:
            echo(f"§c[Miner] Cleanup error: {cleanup_error}")

if __name__ == "__main__":
    main()
