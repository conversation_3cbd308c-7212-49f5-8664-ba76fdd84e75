import discord
import logging
import minescript  # Assuming minescript is working correctly
import time
import threading  # To run the periodic check in a separate thread

# Discord bot token
TOKEN = 'MTA3MTU3OTczNTY4MDg3NjYxNg.GwL6sa.jxGLLt0YCJrofkXm8GZFP9ddbzyZaiVeklf5nc'  # Replace this with your new token
LOG_CHANNEL_ID = 1296328936698937354  # Replace with the correct logging channel ID

# Configure the bot to listen for all intents
intents = discord.Intents.all()

# Set up logging to only show custom messages, suppressing discord.py logs
logging.basicConfig(level=logging.WARNING)  # Suppresses discord.py logs

# Create an instance of the discord bot with the required intents
client = discord.Client(intents=intents)

# Minecraft Minescript setup
def send_to_minecraft(message_content):
    """Send messages or commands to Minecraft."""
    try:
        # Check if the message starts with a command "/"
        if message_content.startswith('/'):
            minescript.execute(message_content)  # Execute Minecraft command
        else:
            minescript.chat(message_content)  # Send regular chat message to <PERSON><PERSON>
    except Exception as e:
        print(f"Failed to send message to <PERSON><PERSON>: {e}")

async def on_chat_message(message):
    """Handle a chat message and forward it to Discord if it contains 'You', '?' and ':'."""
    # Check if the message contains "You", "?" and ":"
    if "You" in message and "?" in message and ":" in message:
        # Log the entire message in the specified Discord channel
        log_channel = client.get_channel(LOG_CHANNEL_ID)
        if log_channel:
            await log_channel.send(message)  # Send the whole message to Discord
        else:
            print(f"Log channel with ID {LOG_CHANNEL_ID} not found.")

def monitor_minecraft_chat():
    """Continuously monitor Minecraft chat for messages containing 'You', '?' and ':'."""
    with minescript.EventQueue() as event_queue:
        # Register chat listener to capture chat messages
        event_queue.register_chat_listener()

        while True:
            # Get the next event
            event = event_queue.get()

            # Process chat events
            if event.type == minescript.EventType.CHAT:
                # Handle the chat message asynchronously
                client.loop.create_task(on_chat_message(event.message))

            # Use a very short sleep to avoid hogging CPU
            time.sleep(0.01)  # 10 ms sleep

# Event listener for when the bot has connected to Discord
@client.event
async def on_ready():
    print("Discord started")

# Event listener for Discord messages
@client.event
async def on_message(message):
    # Handle Discord messages here
    if message.guild:
        # Check if the message starts with the "!" prefix and is from the whitelisted user
        if message.content.startswith('!') and message.author.id == 572991971359195138:  # Whitelisted user ID
            if message.channel.id == 1296328936698937354:  # Correct channel ID
                # Remove the "!" prefix before sending to Minecraft
                message_content = message.content[1:].strip()
                if message_content:
                    send_to_minecraft(message_content)  # Send the text content or command to Minecraft

# Start a separate thread to monitor Minecraft chat for messages
threading.Thread(target=monitor_minecraft_chat, daemon=True).start()

# Run the Discord bot
client.run(TOKEN)
