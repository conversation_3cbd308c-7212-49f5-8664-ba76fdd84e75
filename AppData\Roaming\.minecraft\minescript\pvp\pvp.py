#=============================================================================
# PVP UTILITIES - ENHANCED EDITION
#=============================================================================
# Original by Cerv 😈 (Discord: itscerv)
# Enhanced by Augment AI
# Version: 2.0.0
#=============================================================================

import time
import re
import threading
import tkinter as tk
from tkinter import messagebox, colorchooser

# Try to import CustomTkinter, install if not available
try:
    import customtkinter as ctk
except ImportError:
    import subprocess
    import sys

    # Show a message that we're installing CustomTkinter
    print("CustomTkinter not found. Installing...")

    # Install CustomTkinter using pip
    subprocess.check_call([sys.executable, "-m", "pip", "install", "customtkinter"])

    # Now import CustomTkinter
    import customtkinter as ctk

# Set appearance mode and default color theme
ctk.set_appearance_mode("System")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

# Import our item creator tab module
try:
    from item_creator_tab import create_item_creator_tab
except ImportError:
    # If the module is not found, we'll create a simple placeholder
    def create_item_creator_tab(tabview, item_configs=None, save_callback=None):
        """Create a simple placeholder for the Item Creator tab."""
        item_creator_tab = tabview.add("Add New Item")

        # Create a simple placeholder
        item_creator_frame = ctk.CTkFrame(item_creator_tab)
        item_creator_frame.pack(fill='both', expand=True, padx=20, pady=20)

        placeholder_label = ctk.CTkLabel(
            item_creator_frame,
            text="Item Creator functionality will be implemented in a future update.",
            font=ctk.CTkFont(size=14)
        )
        placeholder_label.pack(expand=True, padx=20, pady=20)

        # Add a button to view existing items
        manage_button = ctk.CTkButton(
            item_creator_frame,
            text="View Existing Items",
            command=lambda: messagebox.showinfo("Items", "Item management will be available in a future update.")
        )
        manage_button.pack(pady=10)

        return item_creator_tab
from minescript import EventQueue, EventType
import minescript
import os
import queue
import sys
import traceback
import json
from datetime import datetime

# Set up version information
SCRIPT_VERSION = "2.0.0"
SCRIPT_DATE = "2024"

# Set global flag for draw_text availability
DRAW_TEXT_AVAILABLE = False
USE_EFFECTS = False  # Disable text effects like shadows and outlines for now

# Try to import our enhanced draw_text module first
try:
    from enhanced_draw_text import (
        draw_centered_string as enhanced_draw_centered_string,
        draw_centered_string_with_effects,
        EnhancedText
    )
    DRAW_TEXT_AVAILABLE = True
    print("Successfully imported enhanced_draw_text")
    minescript.echo("§a§lUsing enhanced text rendering")
except Exception as e:
    print(f"Error importing enhanced_draw_text: {e}")
    traceback.print_exc()

    # Fall back to original draw_text if enhanced version fails
    try:
        # Just check if we can import it, we'll use our safe_draw_centered_string anyway
        import draw_text  # noqa
        DRAW_TEXT_AVAILABLE = True
        print("Successfully imported draw_text")
        minescript.echo("§e§lUsing standard text rendering")
    except Exception as e2:
        print(f"Error importing draw_text: {e2}")
        DRAW_TEXT_AVAILABLE = False
        minescript.echo("§c§lText rendering unavailable - using chat fallback")

# Fallback text object class if draw_text fails
class FallbackTextObject:
    """Fallback text object that works when on-screen rendering is unavailable."""
    def __init__(self, text, color):
        self.text = text
        self.color = color
        self.visible = True
        self.string = self
        self.x = 0
        self.y = 0
        self.scale = 1.0
        self.shadow = False
        self.outline = False

    def set_value(self, new_text):
        """Update the text value."""
        self.text = new_text
        # Display the updated text in chat if it's visible and not empty
        if self.visible and new_text and new_text.strip():
            self._display_in_chat()

    def set_position(self, x, y):
        """Update the position (for compatibility)."""
        self.x = x
        self.y = y

    def set_color(self, color):
        """Update the color (for compatibility)."""
        self.color = color

    def set_scale(self, scale):
        """Update the scale (for compatibility)."""
        self.scale = scale

    def set_visible(self, visible):
        """Set visibility of the text."""
        self.visible = visible
        if visible and self.text and self.text.strip():
            self._display_in_chat()

    def _display_in_chat(self):
        """Display the text in chat with appropriate color."""
        # Choose color prefix based on the color value
        if self.color == 0xFF0000:  # Red
            prefix = "§c"
        elif self.color == 0x00FF00:  # Green
            prefix = "§a"
        elif self.color == 0xFFFF00:  # Yellow
            prefix = "§e"
        elif self.color == 0xFFD700:  # Gold
            prefix = "§6"
        elif self.color == 0xFF7700:  # Orange
            prefix = "§6"
        else:  # Default to white
            prefix = "§f"

        # Display the message
        minescript.echo(f"{prefix}[Display at {self.x},{self.y}] {self.text}")

    def __str__(self):
        """Return the text string."""
        return self.text

# Function to safely draw text with fallback to chat if needed
def safe_draw_centered_string(text, x, y, color, scale=1.0, shadow=True, outline=False):
    """Safely draw text on screen with enhanced visuals, falling back to chat if needed.

    Args:
        text: The text to display
        x, y: Position coordinates
        color: Text color (hex value)
        scale: Text scale
        shadow: Whether to draw text with shadow
        outline: Whether to draw text with outline

    Returns:
        A text object that can be used to update the text
    """
    # Skip rendering if text is empty
    if not text or not text.strip():
        return FallbackTextObject("", color)

    # Use enhanced rendering if available
    if DRAW_TEXT_AVAILABLE:
        try:
            if USE_EFFECTS and (shadow or outline):
                return draw_centered_string_with_effects(text, x, y, color, scale, shadow, outline)
            else:
                return enhanced_draw_centered_string(text, x, y, color, scale)
        except Exception as e:
            print(f"Error using enhanced draw_centered_string: {e}")
            traceback.print_exc()
            minescript.echo(f"§c§lError displaying text on screen: {e}")
            minescript.echo("§e§lSwitching to chat display mode")
            return FallbackTextObject(text, color)
    else:
        # Use fallback if draw_text is not available
        return FallbackTextObject(text, color)

# Path to the config files
CONFIG_FILE_PATH = os.path.expandvars(r'minescript\pvpconfig.json')
ITEMS_FILE_PATH = os.path.expandvars(r'minescript\pvpitems.json')

# Default configuration
DEFAULT_CONFIG = {
    # Display settings
    "runes_message_x": 238,
    "runes_message_y": 80,
    "chat_game_x": 47,
    "chat_game_y": 15,

    # Visual settings
    "text_scale": 2.0,
    "use_shadows": False,  # Disabled by default due to compatibility issues
    "use_outlines": False,
    "runes_text_color": 0x00FF00,  # Green
    "warning_text_color": 0xFF0000,  # Red
    "timer_text_color": 0xFFD700,  # Gold

    # Feature toggles
    "show_rune_messages": True,
    "show_chat_game_message": True,
    "show_runic_obstruction": True,
    "show_anc_helm_proc": True,
    "show_dasher_message": True,
    "show_static_disarm_message": True,
    "show_honorable_blade_message": True,
    "show_quantum_chestplate_message": True,
    "show_panda_helmet_message": True,
    "show_spooky_helm_message": True,
    "show_cupid_boots_message": True
}

# Global variables to store chat messages and their text objects
chat_messages = []
chat_text_objects = []
runes_chat_messages = []
runes_chat_text_objects = []
chat_game_messages = []
chat_game_text_objects = []
test_message_displayed = False  # Flag to track the visibility of the test message
test_message_object = None  # Store the text object for the test message
last_update_time = time.time()  # Track time for message updates
chat_game_timer_active = False  # Flag to indicate if the chat game timer is active
chat_game_timer_duration = 300  # 5-minute countdown timer (300 seconds)
chat_game_start_time = 0  # Store the start time of the chat game
current_chat_game_message = "Waiting for a chat game to start"

# Initialize config variables with defaults
config = DEFAULT_CONFIG.copy()

# Shorthand accessors for commonly used config values
def get_config(key, default=None):
    """Get a configuration value with fallback to default."""
    return config.get(key, default)

# Define properties for easier access to common config values
@property
def runes_message_x():
    return get_config("runes_message_x")

@runes_message_x.setter
def runes_message_x(value):
    config["runes_message_x"] = value
    save_config()

@property
def runes_message_y():
    return get_config("runes_message_y")

@runes_message_y.setter
def runes_message_y(value):
    config["runes_message_y"] = value
    save_config()

@property
def chat_game_x():
    return get_config("chat_game_x")

@chat_game_x.setter
def chat_game_x(value):
    config["chat_game_x"] = value
    save_config()

@property
def chat_game_y():
    return get_config("chat_game_y")

@chat_game_y.setter
def chat_game_y(value):
    config["chat_game_y"] = value
    save_config()

@property
def show_rune_messages():
    return get_config("show_rune_messages")

@show_rune_messages.setter
def show_rune_messages(value):
    config["show_rune_messages"] = value
    save_config()

@property
def show_chat_game_message():
    return get_config("show_chat_game_message")

@show_chat_game_message.setter
def show_chat_game_message(value):
    config["show_chat_game_message"] = value
    save_config()

@property
def show_runic_obstruction():
    return get_config("show_runic_obstruction")

@show_runic_obstruction.setter
def show_runic_obstruction(value):
    config["show_runic_obstruction"] = value
    save_config()

@property
def show_anc_helm_proc():
    return get_config("show_anc_helm_proc")

@show_anc_helm_proc.setter
def show_anc_helm_proc(value):
    config["show_anc_helm_proc"] = value
    save_config()

@property
def show_dasher_message():
    return get_config("show_dasher_message")

@show_dasher_message.setter
def show_dasher_message(value):
    config["show_dasher_message"] = value
    save_config()

@property
def show_static_disarm_message():
    return get_config("show_static_disarm_message")

@show_static_disarm_message.setter
def show_static_disarm_message(value):
    config["show_static_disarm_message"] = value
    save_config()

@property
def show_honorable_blade_message():
    return get_config("show_honorable_blade_message")

@show_honorable_blade_message.setter
def show_honorable_blade_message(value):
    config["show_honorable_blade_message"] = value
    save_config()

@property
def show_quantum_chestplate_message():
    return get_config("show_quantum_chestplate_message")

@show_quantum_chestplate_message.setter
def show_quantum_chestplate_message(value):
    config["show_quantum_chestplate_message"] = value
    save_config()

@property
def show_panda_helmet_message():
    return get_config("show_panda_helmet_message")

@show_panda_helmet_message.setter
def show_panda_helmet_message(value):
    config["show_panda_helmet_message"] = value
    save_config()

@property
def show_spooky_helm_message():
    return get_config("show_spooky_helm_message")

@show_spooky_helm_message.setter
def show_spooky_helm_message(value):
    config["show_spooky_helm_message"] = value
    save_config()

@property
def show_cupid_boots_message():
    return get_config("show_cupid_boots_message")

@show_cupid_boots_message.setter
def show_cupid_boots_message(value):
    config["show_cupid_boots_message"] = value
    save_config()


# Item configuration system - makes it easy to add new items
ITEM_CONFIGS = {
    # Each item has its own configuration dictionary
    "honorable_blade": {
        "display_name": "Imp Sword",
        "config_key": "show_honorable_blade_message",
        "pattern": r"RUNES.*You have dashed forward using your Honorable Blade (.*)!",
        "message_template": "You can use Imp Sword again in {time_display}",
        "cooldowns": {
            "I": 60,
            "II": 55,
            "III": 50,
            "IV": 45,
            "V": 40
        },
        "color": 0x00FF00,  # Green
        "extract_tier": True
    },

    "ancient_helm": {
        "display_name": "Ancient Helm",
        "config_key": "show_anc_helm_proc",
        "pattern": r"RUNES.*(.*) has a combo of 3 on you! Your Ra's Wrath (IV|V) has activated!",
        "negative_pattern": r"RUNES.*Your combo of 3 on (.*) has activated their Ra's Wrath (IV|V)!",
        "message_template": "Ancient Helm procced on {player} for {time_display}",
        "negative_message_template": "{player}'s Ancient Helm procced on you for {time_display}",
        "durations": {
            "IV": 7,  # T4
            "V": 9    # T5
        },
        "color": 0x00FF00,  # Green for positive
        "negative_color": 0xFF0000,  # Red for negative
        "extract_tier": True
    },

    "quantum_chestplate": {
        "display_name": "Quantum Chestplate",
        "config_key": "show_quantum_chestplate_message",
        "pattern": r"RUNES.*You activated Rewind (I|II|III|IV|V)! Lets try that again!",
        "message_template": "You can use Quantum Chestplate {tier} again in {time_display}",
        "cooldowns": {
            "I": 9 * 60,    # 9 minutes
            "II": 8 * 60,   # 8 minutes
            "III": 7 * 60,  # 7 minutes
            "IV": 6 * 60,   # 6 minutes
            "V": 5 * 60     # 5 minutes
        },
        "color": 0xFFD700,  # Gold
        "extract_tier": True
    },

    "panda_helmet": {
        "display_name": "Panda Helmet",
        "config_key": "show_panda_helmet_message",
        "pattern": r"RUNES.*Your Sneeze (V|IV|III|II|I)",
        "message_template": "You can use Panda Helmet in {time_display}",
        "cooldown": 120,  # Fixed 2-minute cooldown for all tiers
        "color": 0xFFD700,  # Gold
        "extract_tier": False
    },

    "spooky_helm": {
        "display_name": "Spooky Helm",
        "config_key": "show_spooky_helm_message",
        "pattern": r"RUNES.*Your Illusion (V|IV|III|II|I)",
        "message_template": "You can use Spooky Helm in {time_display}",
        "cooldown": 180,  # Fixed 3-minute cooldown for all tiers
        "color": 0xFFD700,  # Gold
        "extract_tier": False
    },

    "cupid_boots": {
        "display_name": "Cupid Boots",
        "config_key": "show_cupid_boots_message",
        "pattern": r"RUNES.*Your Intoxicating Love (V|IV|III|II|I)",
        "message_template": "You can use Cupid Boots in {time_display}",
        "cooldown": 90,  # Fixed 1.5-minute cooldown for all tiers
        "color": 0xFFD700,  # Gold
        "extract_tier": False
    },

    "runic_obstruction": {
        "display_name": "Runic Obstruction",
        "config_key": "show_runic_obstruction",
        "pattern": r"RUNES.*You have disabled the rune effects of (.*) with your Runic Obstruction .* for (\d+) seconds.",
        "negative_pattern": r"RUNES.*(.*) has prevented your custom enchants from working for (\d+) seconds with their Runic Obstruction .*.",
        "message_template": "{player} is RUNICED for {duration} seconds",
        "negative_message_template": "You are RUNICED for {duration} seconds",
        "color": 0x00FF00,  # Green for positive
        "negative_color": 0xFF0000,  # Red for negative
        "extract_duration": True
    },

    "dasher": {
        "display_name": "Dasher",
        "config_key": "show_dasher_message",
        "pattern": r"RUNES.*Dashed! Your Dasher (.*) is usable again in: (\d+)s",
        "message_template": "You can use Dasher again in {duration} seconds",
        "color": 0x00FF00,  # Green
        "extract_duration": True
    },

    "static_disarm": {
        "display_name": "Static Disarm",
        "config_key": "show_static_disarm_message",
        "pattern": r"RUNES.*(.*)'s Static Disarm (.*) reduced your attack speed by (\d+)% for (\d+) seconds",
        "message_template": "{player} reduced your attack speed by {percent}% for {duration} seconds",
        "color": 0xFF0000,  # Red
        "extract_duration": True
    }
}

# Compile regular expressions for all item patterns
item_patterns = {}
for item_id, config in ITEM_CONFIGS.items():
    # Compile the main pattern
    if "pattern" in config:
        item_patterns[item_id] = re.compile(config["pattern"])

    # Compile the negative pattern if it exists
    if "negative_pattern" in config:
        item_patterns[f"{item_id}_negative"] = re.compile(config["negative_pattern"])

# Chat game pattern (not part of item configs)
pattern_chat_game = re.compile(r"! (.*) got the answer (.*) in (\d+) seconds!")

# Store the last time the countdown was updated
last_update_time = time.time()

# Config file management functions
def read_config():
    """Read configuration from JSON config file."""
    global config

    try:
        if os.path.exists(CONFIG_FILE_PATH):
            with open(CONFIG_FILE_PATH, 'r', encoding='utf-8') as file:
                loaded_config = json.load(file)

                # Update our config with loaded values
                for key, value in loaded_config.items():
                    config[key] = value

            minescript.echo("§a§lConfig loaded successfully")
        else:
            # If config doesn't exist, create it with default values
            save_config()
            minescript.echo("§e§lCreated new config file with default settings")
    except Exception as e:
        minescript.echo(f"§c§lError loading config: {e}")
        traceback.print_exc()
        # Continue with default values
        config = DEFAULT_CONFIG.copy()
        # Try to save the default config
        try:
            save_config()
        except:
            minescript.echo("§c§lFailed to save default config")

def save_config():
    """Save current configuration to JSON config file."""
    try:
        # Ensure the directory exists
        config_dir = os.path.dirname(CONFIG_FILE_PATH)
        os.makedirs(config_dir, exist_ok=True)

        # Save the config as JSON
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as file:
            json.dump(config, file, indent=4)

        return True
    except Exception as e:
        minescript.echo(f"§c§lError saving config: {e}")
        traceback.print_exc()
        return False

def reset_config():
    """Reset configuration to default values."""
    global config
    config = DEFAULT_CONFIG.copy()
    save_config()
    minescript.echo("§a§lConfig reset to default values")

def update_config_value(key, value):
    """Update a single config value and save the config."""
    if key in config:
        config[key] = value
        save_config()
        return True
    return False

def save_items():
    """Save item configurations to a separate JSON file."""
    try:
        # Ensure the directory exists
        items_dir = os.path.dirname(ITEMS_FILE_PATH)
        os.makedirs(items_dir, exist_ok=True)

        # Save the items as JSON
        with open(ITEMS_FILE_PATH, 'w', encoding='utf-8') as file:
            json.dump(ITEM_CONFIGS, file, indent=4)

        minescript.echo("§a§lItem configurations saved successfully")
        return True
    except Exception as e:
        minescript.echo(f"§c§lError saving item configurations: {e}")
        traceback.print_exc()
        return False

def load_items():
    """Load item configurations from a separate JSON file."""
    global ITEM_CONFIGS, item_patterns

    try:
        if os.path.exists(ITEMS_FILE_PATH):
            with open(ITEMS_FILE_PATH, 'r', encoding='utf-8') as file:
                loaded_items = json.load(file)

                # Update our ITEM_CONFIGS with loaded values
                ITEM_CONFIGS.update(loaded_items)

                # Recompile patterns
                for item_id, config in ITEM_CONFIGS.items():
                    if "pattern" in config:
                        item_patterns[item_id] = re.compile(config["pattern"])

                    if "negative_pattern" in config:
                        item_patterns[f"{item_id}_negative"] = re.compile(config["negative_pattern"])

            minescript.echo("§a§lItem configurations loaded successfully")
            return True
        else:
            # If items file doesn't exist, create it with default values
            save_items()
            minescript.echo("§e§lCreated new items file with default settings")
            return True
    except Exception as e:
        minescript.echo(f"§c§lError loading item configurations: {e}")
        traceback.print_exc()
        return False

# Update and display the test message
def toggle_test_message():
    """Toggle the display of a test message to check positioning."""
    global test_message_displayed, test_message_object, DRAW_TEXT_AVAILABLE

    if test_message_displayed:
        # Remove the test message if it's displayed
        if test_message_object and test_message_object in runes_chat_text_objects:
            runes_chat_text_objects.remove(test_message_object)
        if test_message_object:  # Only clear the text if the object exists
            test_message_object.string.set_value("")  # Clear the text
        test_message_displayed = False
        minescript.echo("§7Test message removed")
    else:
        # Show the test message if it's not displayed
        if DRAW_TEXT_AVAILABLE:
            minescript.echo("§a§lTest Message: §r§aDisplaying a test message at position")
            test_message_object = display_runes_message("Test message at position", 0x00ff00, 5)
            test_message_displayed = True
        else:
            minescript.echo("§e§lTest Message: §r§eDisplaying a test message in chat (on-screen display unavailable)")
            test_message_object = display_runes_message("Test message at position", 0x00ff00, 5)
            test_message_displayed = True

def update_runes_message_x(value):
    global runes_message_x
    runes_message_x = int(float(value))
    save_config()

def update_runes_message_y(value):
    global runes_message_y
    runes_message_y = int(float(value))
    save_config()

def update_chat_game_x(value):
    global chat_game_x
    chat_game_x = int(float(value))
    save_config()

def update_chat_game_y(value):
    global chat_game_y
    chat_game_y = int(float(value))
    save_config()

def toggle_rune_messages():
    global show_rune_messages
    show_rune_messages = not show_rune_messages
    save_config()

def toggle_chat_game_message():
    global show_chat_game_message, chat_game_messages, chat_game_text_objects

    show_chat_game_message = not show_chat_game_message
    save_config()

    if not show_chat_game_message:
        # Clear chat game messages when toggled off
        if chat_game_messages and chat_game_text_objects:
            chat_game_messages.clear()
            chat_game_text_objects.clear()

def toggle_quantum_chestplate_message():
    global show_quantum_chestplate_message
    show_quantum_chestplate_message = not show_quantum_chestplate_message
    save_config()

def toggle_panda_helmet_message():
    global show_panda_helmet_message
    show_panda_helmet_message = not show_panda_helmet_message
    save_config()

def toggle_spooky_helm_message():
    global show_spooky_helm_message
    show_spooky_helm_message = not show_spooky_helm_message
    save_config()

def toggle_cupid_boots_message():
    global show_cupid_boots_message
    show_cupid_boots_message = not show_cupid_boots_message
    save_config()

def toggle_runic_obstruction():
    global show_runic_obstruction
    show_runic_obstruction = not show_runic_obstruction
    save_config()

def toggle_anc_helm_proc():
    global show_anc_helm_proc
    show_anc_helm_proc = not show_anc_helm_proc
    save_config()

def toggle_dasher_message():
    global show_dasher_message
    show_dasher_message = not show_dasher_message
    save_config()

def toggle_static_disarm_message():
    global show_static_disarm_message
    show_static_disarm_message = not show_static_disarm_message
    save_config()

def toggle_honorable_blade_message():
    global show_honorable_blade_message
    show_honorable_blade_message = not show_honorable_blade_message
    save_config()


def filter_and_format_message(message):
    """Process a chat message and format it for display if it matches any patterns.

    This function uses the ITEM_CONFIGS dictionary to check for matches and format messages.

    Args:
        message: The chat message to process

    Returns:
        A tuple of (formatted_message, color, duration) or None if no match
    """
    global current_chat_game_message

    # Process the message
    pass

    # Check for chat game messages first (special case)
    chat_game_match = pattern_chat_game.search(message)
    if chat_game_match:
        name, answer, time_taken = chat_game_match.groups()
        current_chat_game_message = f"{name} got the answer in {time_taken}s: {answer}"
        start_chat_game_timer()
        return None

    # Process each item configuration
    for item_id, config in ITEM_CONFIGS.items():
        # Skip if this item's messages are disabled
        config_key = config.get("config_key")
        if config_key and not get_config(config_key, True):
            continue

        # Check for main pattern match
        pattern = item_patterns.get(item_id)
        if pattern and pattern.search(message):
            match = pattern.search(message)

            # Handle special cases for items with direct cooldown triggers
            if item_id == "panda_helmet":
                start_panda_helmet_cooldown()
                return None
            elif item_id == "spooky_helm":
                start_spooky_helm_cooldown()
                return None
            elif item_id == "cupid_boots":
                start_cupid_boots_cooldown()
                return None
            elif item_id == "quantum_chestplate" and match:
                tier = match.group(1)
                cooldown = config["cooldowns"].get(tier, 9 * 60)
                start_quantum_chestplate_cooldown(tier, cooldown)
                return None

            # Process regular matches
            if match:
                # Get the message template
                template = config.get("message_template", "")

                # Format parameters for the template
                params = {}

                # Extract tier if needed
                if config.get("extract_tier", False) and len(match.groups()) > 0:
                    tier = match.group(1)
                    params["tier"] = tier

                    # Get cooldown based on tier if available
                    if "cooldowns" in config:
                        cooldown = config["cooldowns"].get(tier, 60)
                        params["cooldown"] = cooldown
                        params["time_display"] = format_time(cooldown)
                    elif "durations" in config:
                        duration = config["durations"].get(tier, 7)
                        params["duration"] = duration
                        params["time_display"] = f"{duration} seconds"

                # Special case for Lark to extract both percentage and duration
                if item_id == "lark":
                    # For Lark, we need to extract percentage and duration
                    try:
                        # For Lark, the pattern should be:
                        # RUNES ✔ Your Lark (I|II|III|IV|V) has activated, it deals ([0-9.]+)% more damage for ([0-9]+) seconds.

                        # Extract tier (group 1)
                        if len(match.groups()) >= 1:
                            tier = match.group(1)
                            params["tier"] = tier

                        # Extract percentage (group 2)
                        if len(match.groups()) >= 2:
                            percent = match.group(2)
                            if percent:
                                params["percent"] = percent

                        # Extract duration (group 3)
                        if len(match.groups()) >= 3:
                            duration_str = match.group(3)
                            if duration_str and duration_str.isdigit():
                                duration = int(duration_str)
                                params["duration"] = duration
                                params["time_display"] = f"{duration} seconds"

                        # If we didn't find the duration in the expected position, try to find it in any group
                        if "duration" not in params:
                            for _, group in enumerate(match.groups()):
                                if group and group.isdigit():
                                    duration = int(group)
                                    params["duration"] = duration
                                    params["time_display"] = f"{duration} seconds"
                                    break
                    except Exception as e:
                        # Silently handle errors
                        pass

                # Extract duration if needed for other items
                elif config.get("extract_duration", False):
                    # Find the duration in the match groups
                    for _, group in enumerate(match.groups()):
                        if group and group.isdigit():
                            duration = int(group)
                            params["duration"] = duration
                            params["time_display"] = f"{duration} seconds"
                            break

                # Extract player name if available (usually first group)
                if len(match.groups()) > 0 and not "tier" in params:
                    player = match.group(1)
                    if player and not player.isdigit():
                        params["player"] = player

                # Extract percentage if available
                if "percent" in template:
                    for group in match.groups():
                        if group and group.isdigit():
                            params["percent"] = group

                # For fixed cooldown items
                if "cooldown" in config and not "cooldown" in params:
                    cooldown = config["cooldown"]
                    params["cooldown"] = cooldown
                    params["time_display"] = format_time(cooldown)

                # Format the message
                try:
                    formatted_message = template.format(**params)
                    color = config.get("color", 0x00FF00)
                    duration = params.get("cooldown", params.get("duration", 60))
                    return (formatted_message, color, duration)
                except KeyError as e:
                    print(f"Error formatting message for {item_id}: {e}")
                    print(f"Template: {template}, Params: {params}")
                    continue

        # Check for negative pattern match
        negative_pattern = item_patterns.get(f"{item_id}_negative")
        if negative_pattern and negative_pattern.search(message):
            match = negative_pattern.search(message)

            # Get the negative message template
            template = config.get("negative_message_template", "")

            # Format parameters for the template
            params = {}

            # Extract tier if needed
            if config.get("extract_tier", False) and len(match.groups()) > 0:
                tier = match.group(1)
                params["tier"] = tier

                # Get duration based on tier if available
                if "durations" in config:
                    duration = config["durations"].get(tier, 7)
                    params["duration"] = duration
                    params["time_display"] = f"{duration} seconds"

            # Extract duration if needed
            if config.get("extract_duration", False):
                # Find the duration in the match groups
                for group in match.groups():
                    if group and group.isdigit():
                        duration = int(group)
                        params["duration"] = duration
                        params["time_display"] = f"{duration} seconds"
                        break

            # Extract player name if available (usually first group)
            if len(match.groups()) > 0 and not "tier" in params:
                player = match.group(1)
                if player and not player.isdigit():
                    params["player"] = player

            # Format the message
            try:
                formatted_message = template.format(**params)
                color = config.get("negative_color", 0xFF0000)
                duration = params.get("duration", 60)
                return (formatted_message, color, duration)
            except KeyError as e:
                print(f"Error formatting negative message for {item_id}: {e}")
                print(f"Template: {template}, Params: {params}")
                continue

    # No match found
    return None


def format_time(seconds):
    """Format seconds into a readable time string.

    Args:
        seconds: The number of seconds

    Returns:
        A formatted string like "5:30" for 5 minutes and 30 seconds
    """
    minutes = seconds // 60
    remaining_seconds = seconds % 60
    return f"{minutes}:{remaining_seconds:02d}"

def start_item_cooldown(item_id, tier=None, duration=None):
    """Generic function to start a cooldown timer for any item.

    Args:
        item_id: The ID of the item in ITEM_CONFIGS
        tier: The tier of the item (optional)
        duration: Override the default duration (optional)
    """
    global runes_chat_messages, runes_chat_text_objects

    # Get the item config
    if item_id not in ITEM_CONFIGS:
        print(f"Error: Unknown item ID: {item_id}")
        return

    config = ITEM_CONFIGS[item_id]

    # Only display if the toggle is on
    config_key = config.get("config_key")
    if config_key and not get_config(config_key, True):
        return

    # Get the display name
    display_name = config.get("display_name", item_id)

    # Determine the cooldown duration
    cooldown = None
    if duration is not None:
        # Use the provided duration
        cooldown = duration
    elif tier is not None and "cooldowns" in config:
        # Use tier-specific cooldown
        cooldown = config["cooldowns"].get(tier, 60)
    elif "cooldown" in config:
        # Use fixed cooldown
        cooldown = config["cooldown"]
    else:
        # Default cooldown
        cooldown = 60

    # Only clear existing cooldown message for this specific item
    # First, find indices of messages containing this item's display name
    indices_to_remove = []
    for i, (msg, _, _) in enumerate(runes_chat_messages):
        if display_name in msg:
            indices_to_remove.append(i)

    # Remove messages and text objects in reverse order to avoid index issues
    for i in sorted(indices_to_remove, reverse=True):
        if i < len(runes_chat_messages):
            runes_chat_messages.pop(i)
        if i < len(runes_chat_text_objects):
            runes_chat_text_objects.pop(i)

    # Format the message
    params = {
        "tier": tier if tier else "",
        "time_display": format_time(cooldown),
        "cooldown": cooldown,
        "duration": cooldown
    }

    # Get the message template
    template = config.get("message_template", "You can use {display_name} again in {time_display}")

    try:
        message = template.format(display_name=display_name, **params)
    except KeyError as e:
        print(f"Error formatting cooldown message for {item_id}: {e}")
        message = f"You can use {display_name} again in {format_time(cooldown)}"

    # Get the color
    timer_color = get_config("timer_text_color", config.get("color", 0xFFD700))

    # Add the message
    runes_chat_messages.append((message, timer_color, cooldown))
    display_runes_message(message, timer_color, cooldown)


def start_quantum_chestplate_cooldown(tier, cooldown):
    """Start a cooldown timer for Quantum Chestplate.

    Args:
        tier: The tier of the Quantum Chestplate (I-V)
        cooldown: The cooldown duration in seconds
    """
    start_item_cooldown("quantum_chestplate", tier, cooldown)

def start_panda_helmet_cooldown():
    """Start a cooldown timer for Panda Helmet."""
    start_item_cooldown("panda_helmet")

def start_spooky_helm_cooldown():
    """Start a cooldown timer for Spooky Helm."""
    start_item_cooldown("spooky_helm")

def start_cupid_boots_cooldown():
    """Start a cooldown timer for Cupid Boots."""
    start_item_cooldown("cupid_boots")

def remove_duplicate_message(message, message_list, text_object_list):
    """Remove an old duplicate message from the given message list and text object list."""
    # Extract the base part of the message (before "for x seconds") to compare
    base_message = message.split('for')[0]

    # Check if the message with the same base content already exists in the given list
    for i, (msg, *_) in enumerate(message_list):  # Use *_ to collect all unused variables
        if base_message in msg:
            # Remove the old message and text object
            message_list.pop(i)
            text_object_list.pop(i)
            break

def display_runes_message(msg, color, time_left):
    """Display a runes message on screen with enhanced visuals.

    Args:
        msg: The message text to display
        color: The color of the text (hex value)
        time_left: The time in seconds the message should remain (used for tracking)

    Returns:
        The text object created
    """
    global runes_chat_text_objects

    try:
        # Skip if message is empty
        if not msg or not msg.strip():
            return None

        # Get settings from config
        x_pos = get_config("runes_message_x", 238)
        y_pos = get_config("runes_message_y", 80)
        use_shadows = get_config("use_shadows", True)
        use_outlines = get_config("use_outlines", False)
        text_scale = get_config("text_scale", 2.0)

        # Set position and scale for the message
        # Calculate position based on the number of existing messages
        # Each message gets its own line with proper spacing
        message_index = len(runes_chat_text_objects)
        y = y_pos - message_index * 15  # Increased spacing between messages

        # Create the text object using our safe function with visual effects
        text_obj = safe_draw_centered_string(
            msg,
            x=x_pos,
            y=y,
            color=color,
            scale=text_scale,
            shadow=use_shadows,
            outline=use_outlines
        )

        # Add to our list of text objects
        runes_chat_text_objects.append(text_obj)
        return text_obj

    except Exception as e:
        print(f"Error displaying runes message: {e}")
        minescript.echo(f"§c§lError displaying runes message: §r§7{e}")
        traceback.print_exc()
        return None

def toggle_spooky_helm_message():
    global show_spooky_helm_message
    show_spooky_helm_message = not show_spooky_helm_message
    save_config()

# Display Chat Game message
def display_chat_game_message(msg, color, time_left=None):  # time_left is used for tracking in the message list
    """Display a new message for Chat Game messages on screen with enhanced visuals.

    Args:
        msg: The message text to display
        color: The color of the text (hex value)
        time_left: The time in seconds the message should remain (used for tracking)

    Returns:
        The text object created or None if not displayed
    """
    global chat_game_text_objects

    # Only display the message if chat game messages are enabled
    if get_config("show_chat_game_message", True):
        try:
            # Remove old text objects to avoid overlaps
            if chat_game_text_objects:
                chat_game_text_objects.clear()

            # Skip drawing if message is empty
            if not msg or not msg.strip():
                return None

            # Get settings from config
            x_pos = get_config("chat_game_x", 47)
            y_pos = get_config("chat_game_y", 15)
            use_shadows = get_config("use_shadows", True)
            use_outlines = get_config("use_outlines", False)
            text_scale = get_config("text_scale", 2.5)  # Slightly larger for chat game

            # Create a text object with the message using our safe function with visual effects
            text_obj = safe_draw_centered_string(
                msg,
                x=x_pos,
                y=y_pos,
                color=color,
                scale=text_scale,
                shadow=use_shadows,
                outline=use_outlines
            )

            # Only append if successful
            if text_obj:
                chat_game_text_objects.append(text_obj)
                return text_obj
            else:
                print(f"Failed to create text object for chat game message: {msg}")
                return None

        except Exception as e:
            print(f"Error displaying chat game message: {e}")
            minescript.echo(f"§c§lError displaying chat game message: §r§7{e}")
            traceback.print_exc()
            return None
    return None

def format_time(seconds):
    """Format time in seconds to a MM:SS format or just seconds if less than 60."""
    if seconds < 60:
        return f"{seconds} seconds"
    else:
        minutes, secs = divmod(seconds, 60)
        return f"{minutes}:{secs:02d}"

def update_runes_messages():
    """Update all runes messages with countdown timers."""
    global runes_chat_messages, runes_chat_text_objects, last_update_time

    current_time = time.time()
    time_elapsed = current_time - last_update_time

    if time_elapsed >= 1:  # Only update the countdown once a full second has passed
        for i in range(len(runes_chat_messages) - 1, -1, -1):
            msg, color, time_left = runes_chat_messages[i]
            time_left -= int(time_elapsed)

            if time_left <= 0:
                # Remove expired messages
                if i < len(runes_chat_messages):
                    runes_chat_messages.pop(i)
                if i < len(runes_chat_text_objects):
                    runes_chat_text_objects.pop(i)

                # No longer displaying messages in chat when timers expire
            else:
                # Update the countdown for each message with improved formatting
                if "Spooky Helm" in msg:
                    updated_message = f"You can use Spooky Helm in {format_time(time_left)}"
                elif "Panda Helmet" in msg:
                    updated_message = f"You can use Panda Helmet in {format_time(time_left)}"
                elif "Quantum Chestplate" in msg:
                    updated_message = f"You can use Quantum Chestplate again in {format_time(time_left)}"
                elif "Cupid Boots" in msg:
                    updated_message = f"You can use Cupid Boots in {format_time(time_left)}"
                elif "RUNICED" in msg:
                    # Extract the player name if present
                    if "You are RUNICED" in msg:
                        updated_message = f"You are RUNICED for {time_left} seconds"
                    else:
                        name = msg.split(" is RUNICED")[0]
                        updated_message = f"{name} is RUNICED for {time_left} seconds"
                elif "Lark" in msg:
                    # Special case for Lark item
                    # Extract the percentage if it exists
                    percent_match = re.search(r"dealing (\d+\.?\d*)%", msg)
                    percent = percent_match.group(1) if percent_match else ""

                    if percent:
                        updated_message = f"Lark is dealing {percent}% more damage for {time_left} seconds"
                    else:
                        updated_message = f"Lark is activated for {time_left} seconds"

                    # Update the message in the list
                    runes_chat_messages[i] = (updated_message, color, time_left)

                    # Update the text object directly to ensure it's visible
                    if i < len(runes_chat_text_objects) and runes_chat_text_objects[i]:
                        try:
                            runes_chat_text_objects[i].set_value(updated_message)
                        except Exception as e:
                            # If updating fails, try to recreate the text object
                            try:
                                # Get settings from config
                                x_pos = get_config("runes_message_x", 238)
                                y_pos = get_config("runes_message_y", 80)
                                use_shadows = get_config("use_shadows", True)
                                use_outlines = get_config("use_outlines", False)
                                text_scale = get_config("text_scale", 2.0)

                                # Calculate position
                                y = y_pos - i * 15

                                # Remove old text object if it exists
                                if runes_chat_text_objects[i]:
                                    try:
                                        runes_chat_text_objects[i].remove()
                                    except:
                                        pass

                                # Create new text object
                                new_text_obj = safe_draw_centered_string(
                                    updated_message,
                                    x=x_pos,
                                    y=y,
                                    color=color,
                                    scale=text_scale,
                                    shadow=use_shadows,
                                    outline=use_outlines
                                )

                                # Replace in the list
                                runes_chat_text_objects[i] = new_text_obj
                            except:
                                # Silently handle errors
                                pass

                    # No longer displaying updates in chat
                    # Keep the message visible on screen only

                    # Skip the normal update process since we've already updated it
                    continue
                elif "{duration}" in msg:
                    # For messages with {duration} template variable, replace it with the current time_left
                    # First, extract the original message template
                    for item_id, config in ITEM_CONFIGS.items():
                        display_name = config.get("display_name", item_id)
                        if display_name in msg:
                            # Found the matching item
                            template = config.get("message_template", "")
                            if "{duration}" in template:
                                # Create params with updated duration
                                params = {
                                    "duration": time_left,
                                    "time_display": format_time(time_left),
                                    "cooldown": time_left,
                                    "percent": config.get("percent", "")
                                }
                                try:
                                    updated_message = template.format(display_name=display_name, **params)
                                    break
                                except KeyError:
                                    # If formatting fails, fall back to simple replacement
                                    updated_message = re.sub(r"for \d+ seconds", f"for {time_left} seconds", msg)
                            else:
                                # No {duration} in template, use regex replacement
                                updated_message = re.sub(r"for \d+ seconds", f"for {time_left} seconds", msg)
                    else:
                        # No matching item found, use regex replacement
                        updated_message = re.sub(r"for \d+ seconds", f"for {time_left} seconds", msg)
                else:
                    # For other messages, try different patterns
                    if "for " in msg and " seconds" in msg:
                        # Try to update "for X seconds" format
                        updated_message = re.sub(r"for \d+ seconds", f"for {time_left} seconds", msg)
                    elif "in " in msg and " seconds" in msg:
                        # Try to update "in X seconds" format
                        updated_message = re.sub(r"in \d+ seconds", f"in {time_left} seconds", msg)
                    else:
                        # No recognized format, keep the message as is
                        updated_message = msg

                # Update the message in the list
                runes_chat_messages[i] = (updated_message, color, time_left)

                # Update the text object directly
                if i < len(runes_chat_text_objects) and runes_chat_text_objects[i]:
                    try:
                        runes_chat_text_objects[i].set_value(updated_message)
                    except Exception:
                        # If updating fails, try to recreate the text object
                        try:
                            # Get settings from config
                            x_pos = get_config("runes_message_x", 238)
                            y_pos = get_config("runes_message_y", 80)
                            use_shadows = get_config("use_shadows", True)
                            use_outlines = get_config("use_outlines", False)
                            text_scale = get_config("text_scale", 2.0)

                            # Calculate position
                            y = y_pos - i * 15

                            # Remove old text object if it exists
                            if runes_chat_text_objects[i]:
                                try:
                                    runes_chat_text_objects[i].remove()
                                except:
                                    pass

                            # Create new text object
                            new_text_obj = safe_draw_centered_string(
                                updated_message,
                                x=x_pos,
                                y=y,
                                color=color,
                                scale=text_scale,
                                shadow=use_shadows,
                                outline=use_outlines
                            )

                            # Replace in the list
                            runes_chat_text_objects[i] = new_text_obj
                        except:
                            # Silently handle errors
                            pass

                # No longer displaying updates in chat
                # Keep the message visible on screen only

        last_update_time = current_time
def update_chat_game_messages():
    """Update the countdown for each Chat Game message and remove messages when the timer hits zero.
    Uses improved formatting and visual updates.
    """
    global chat_game_messages, chat_game_text_objects, last_update_time, current_chat_game_message

    # Get the current time
    current_time = time.time()

    # Calculate how much time has passed since the last update
    time_elapsed = current_time - last_update_time

    # Update the chat game timer if active
    if chat_game_timer_active:
        update_chat_game_timer()  # Update the timer display

    # Update message countdowns once per second
    if time_elapsed >= 1:
        for i in range(len(chat_game_messages) - 1, -1, -1):
            _, color, time_left = chat_game_messages[i]  # We don't need the msg here
            time_left -= int(time_elapsed)

            if time_left <= 0:
                # Remove expired message
                chat_game_messages.pop(i)
                if i < len(chat_game_text_objects):  # Safety check
                    chat_game_text_objects.pop(i)

                # No longer displaying messages in chat when timers expire
            else:
                # Update the countdown message
                # Use the global chat game message (with countdown)
                chat_game_messages[i] = (current_chat_game_message, color, time_left)

                # Display the updated message in chat if it's a significant change
                # Only show updates at certain intervals to avoid spam
                if time_left % 30 == 0 or time_left <= 10:  # Show at 30-second intervals or final countdown
                    # Use our safe function to display the update
                    safe_draw_centered_string(current_chat_game_message, x=0, y=0, color=color, scale=1.0)

        last_update_time = current_time

def on_chat_message(message):
    global runes_chat_messages, chat_game_messages, chat_game_text_objects

    # Process and filter the message for Runes
    formatted_runes_message = filter_and_format_message(message)
    if formatted_runes_message and show_rune_messages:
        msg, color, time_left = formatted_runes_message
        remove_duplicate_message(msg, runes_chat_messages, runes_chat_text_objects)  # Remove duplicate from Runes
        runes_chat_messages.append(formatted_runes_message)
        display_runes_message(msg, color, time_left)

    # Process and filter the message for Chat Games
    formatted_chat_game_message = filter_and_format_chat_game_message(message)
    if formatted_chat_game_message and show_chat_game_message:
        # Ensure only one countdown message exists
        if chat_game_messages and chat_game_text_objects:
            chat_game_messages.clear()  # Clear any previous countdown messages
            chat_game_text_objects.clear()

        msg, color, time_left = formatted_chat_game_message
        chat_game_messages.append(formatted_chat_game_message)
        display_chat_game_message(msg, color, time_left)

    # Trigger Spooky Helm cooldown if pattern is matched
    if item_patterns.get("spooky_helm") and item_patterns["spooky_helm"].search(message):
        start_spooky_helm_cooldown()


def filter_and_format_chat_game_message(message):
    """Filter the message for chat games and format the output with improved visuals."""
    match_chat_game = pattern_chat_game.search(message)
    if match_chat_game and show_chat_game_message:
        # Extract information but we don't need to use it directly
        # Just knowing it's a chat game message is enough to start the timer

        # Display a message about who won the chat game
        player_name = match_chat_game.group(1)
        answer_text = match_chat_game.group(2)
        time_taken = match_chat_game.group(3)

        # Show a temporary message about who won
        temp_message = f"{player_name} got the answer '{answer_text}' in {time_taken}s!"
        display_chat_game_message(temp_message, 0x00ff00, 3)  # Green color, show for 3 seconds

        # Then start the timer for the next game
        start_chat_game_timer()

        # Return a placeholder - the actual message will be managed by the timer
        return ("", 0x00ff00, 5)

    return None

# Function to start a chat game timer with improved visuals
def start_chat_game_timer():
    """Start or restart the chat game countdown timer with improved visuals."""
    global chat_game_start_time, chat_game_timer_active, chat_game_messages
    global chat_game_text_objects, current_chat_game_message

    # Stop any existing timer before starting a new one
    chat_game_timer_active = False  # Stop any active timer

    # Clear all existing chat game messages
    if chat_game_messages and chat_game_text_objects:
        chat_game_messages.clear()
        chat_game_text_objects.clear()

    # Start the new chat game timer
    chat_game_start_time = time.time()
    chat_game_timer_active = True

    # Initial display of the timer
    update_chat_game_timer()

# Function to update the chat game timer with improved visuals
def update_chat_game_timer():
    """Update the chat game countdown timer with improved visuals."""
    global chat_game_timer_active, chat_game_timer_duration, chat_game_start_time
    global chat_game_messages, chat_game_text_objects, current_chat_game_message

    if chat_game_timer_active:
        time_elapsed = time.time() - chat_game_start_time
        remaining_time = chat_game_timer_duration - int(time_elapsed)

        if remaining_time <= 0:
            # Timer finished
            chat_game_timer_active = False
            current_chat_game_message = "Waiting for a chat game to start"

            # Clear previous countdown and display the waiting message
            if chat_game_messages and chat_game_text_objects:
                chat_game_messages.clear()
                chat_game_text_objects.clear()

            # Display the waiting message in gold color
            display_chat_game_message(current_chat_game_message, 0xffd700, 0)

            # No longer displaying messages in chat when timers expire
        else:
            # Format the countdown with improved visuals
            minutes, seconds = divmod(remaining_time, 60)

            # Change color based on remaining time for visual cue
            if remaining_time < 30:  # Less than 30 seconds - red
                color = 0xff0000
                countdown_message = f"⚠ Next chat game in {minutes}:{seconds:02d} ⚠"
            elif remaining_time < 60:  # Less than 1 minute - orange
                color = 0xff7700
                countdown_message = f"Next chat game in {minutes}:{seconds:02d}"
            else:  # More than 1 minute - gold
                color = 0xffd700
                countdown_message = f"Next chat game in {minutes}:{seconds:02d}"

            # Store the current message for reference elsewhere
            current_chat_game_message = countdown_message

            # Update the countdown
            if not chat_game_messages:
                # If no message exists yet, create one
                chat_game_messages.append((countdown_message, color, remaining_time))

                # Display the message using our safe function
                safe_draw_centered_string(countdown_message, x=0, y=0, color=color, scale=1.0)
            else:
                # Update existing message
                chat_game_messages[0] = (countdown_message, color, remaining_time)

                # Only display updates at certain intervals to avoid spam
                if remaining_time % 30 == 0 or remaining_time <= 10:
                    safe_draw_centered_string(countdown_message, x=0, y=0, color=color, scale=1.0)

# Function to create the GUI with improved layout and visuals
def create_gui():
    """Create a GUI with improved layout and visuals to adjust values using CustomTkinter."""
    # Set appearance mode based on system settings
    ctk.set_appearance_mode("System")  # Options: "System", "Dark", "Light"
    ctk.set_default_color_theme("blue")  # Options: "blue", "green", "dark-blue"

    # Create the main window using CustomTkinter
    root = ctk.CTk()
    root.title("PVP Utils - Enhanced Edition")
    root.geometry("600x650")  # Larger default size for more content

    # Set icon if available
    try:
        root.iconbitmap("minescript/pvp_icon.ico")
    except:
        pass  # Continue without icon if not available

    # Create a tabview (CustomTkinter's version of a notebook)
    tabview = ctk.CTkTabview(root)
    tabview.pack(expand=True, fill="both", padx=10, pady=10)

    # Create tabs
    runes_tab = tabview.add("Runes")
    visuals_tab = tabview.add("Visual Settings")
    chat_games_tab = tabview.add("Chat Games")
    # The Item Creator tab will be created by the create_item_creator_tab function
    about_tab = tabview.add("About")

    # ======= Runes Tab ========
    # Create frames for organization
    position_frame = ctk.CTkFrame(runes_tab)
    position_frame.pack(fill='x', padx=10, pady=5)

    # Create a sub-frame for the grid layout
    position_controls_frame = ctk.CTkFrame(position_frame)
    position_controls_frame.pack(fill='x', padx=10, pady=5)

    # Add a label for the frame
    position_label = ctk.CTkLabel(position_frame, text="Position Settings", font=ctk.CTkFont(size=14, weight="bold"))
    position_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Create a grid layout for position controls
    x_pos_label = ctk.CTkLabel(position_controls_frame, text="Runes Message X Position:")
    x_pos_label.grid(row=0, column=0, padx=10, pady=5, sticky='w')

    x_pos_value = ctk.CTkLabel(position_controls_frame, text=f"{get_config('runes_message_x', 238)}")
    x_pos_value.grid(row=0, column=1, padx=10, pady=5)

    def update_x_pos(value):
        int_value = int(float(value))
        update_config_value("runes_message_x", int_value)
        x_pos_value.configure(text=f"{int_value}")

    x_pos_slider = ctk.CTkSlider(position_controls_frame, from_=0, to=500, width=300,
                               command=update_x_pos)
    x_pos_slider.set(get_config("runes_message_x", 238))
    x_pos_slider.grid(row=1, column=0, columnspan=2, padx=10, pady=5, sticky='ew')

    y_pos_label = ctk.CTkLabel(position_controls_frame, text="Runes Message Y Position:")
    y_pos_label.grid(row=2, column=0, padx=10, pady=5, sticky='w')

    y_pos_value = ctk.CTkLabel(position_controls_frame, text=f"{get_config('runes_message_y', 80)}")
    y_pos_value.grid(row=2, column=1, padx=10, pady=5)

    def update_y_pos(value):
        int_value = int(float(value))
        update_config_value("runes_message_y", int_value)
        y_pos_value.configure(text=f"{int_value}")

    y_pos_slider = ctk.CTkSlider(position_controls_frame, from_=0, to=200, width=300,
                               command=update_y_pos)
    y_pos_slider.set(get_config("runes_message_y", 80))
    y_pos_slider.grid(row=3, column=0, columnspan=2, padx=10, pady=5, sticky='ew')

    # Test button for position
    test_button = ctk.CTkButton(position_controls_frame, text="Test Position", command=toggle_test_message)
    test_button.grid(row=4, column=0, columnspan=2, padx=10, pady=10)

    # ======= Visual Settings Tab ========
    # Text appearance settings
    text_appearance_frame = ctk.CTkFrame(visuals_tab)
    text_appearance_frame.pack(fill='x', padx=10, pady=10)

    # Add a label for the frame
    appearance_label = ctk.CTkLabel(text_appearance_frame, text="Text Appearance", font=ctk.CTkFont(size=14, weight="bold"))
    appearance_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Create a sub-frame for the grid layout
    scale_controls_frame = ctk.CTkFrame(text_appearance_frame)
    scale_controls_frame.pack(fill='x', padx=10, pady=5)

    # Text scale slider
    scale_label = ctk.CTkLabel(scale_controls_frame, text="Text Scale:")
    scale_label.grid(row=0, column=0, padx=10, pady=5, sticky='w')

    text_scale_value = ctk.CTkLabel(scale_controls_frame, text=f"{get_config('text_scale', 2.0):.1f}")
    text_scale_value.grid(row=0, column=1, padx=10, pady=5)

    def update_text_scale(value):
        float_value = float(value)
        update_config_value("text_scale", float_value)
        text_scale_value.configure(text=f"{float_value:.1f}")

    text_scale_slider = ctk.CTkSlider(scale_controls_frame, from_=1.0, to=4.0, width=300,
                                   command=update_text_scale)
    text_scale_slider.set(get_config("text_scale", 2.0))
    text_scale_slider.grid(row=1, column=0, columnspan=2, padx=10, pady=5, sticky='ew')

    # Text effects switches
    effects_frame = ctk.CTkFrame(text_appearance_frame)
    effects_frame.pack(fill='x', padx=10, pady=10)

    # Warning label about effects
    warning_label = ctk.CTkLabel(
        effects_frame,
        text="Note: Text effects may not work on all Minecraft versions",
        text_color="red"
    )
    warning_label.pack(side='top', pady=5, fill='x')

    # Switches in a new frame
    switch_frame = ctk.CTkFrame(effects_frame)
    switch_frame.pack(side='top', fill='x')

    use_shadows_var = tk.BooleanVar(value=get_config("use_shadows", False))
    use_shadows_switch = ctk.CTkSwitch(
        switch_frame,
        text="Use Text Shadows",
        variable=use_shadows_var,
        command=lambda: update_config_value("use_shadows", use_shadows_var.get())
    )
    use_shadows_switch.pack(side='left', padx=20, pady=10)

    use_outlines_var = tk.BooleanVar(value=get_config("use_outlines", False))
    use_outlines_switch = ctk.CTkSwitch(
        switch_frame,
        text="Use Text Outlines",
        variable=use_outlines_var,
        command=lambda: update_config_value("use_outlines", use_outlines_var.get())
    )
    use_outlines_switch.pack(side='left', padx=20, pady=10)

    # Color settings
    color_frame = ctk.CTkFrame(visuals_tab)
    color_frame.pack(fill='x', padx=10, pady=10)

    # Add a label for the frame
    color_title = ctk.CTkLabel(color_frame, text="Text Colors", font=ctk.CTkFont(size=14, weight="bold"))
    color_title.pack(anchor="w", padx=10, pady=(10, 5))

    # Function to create a color picker button
    def create_color_picker(parent, _, label, config_key, default_color):
        color_row = ctk.CTkFrame(parent)
        color_row.pack(fill='x', padx=10, pady=5)

        label_widget = ctk.CTkLabel(color_row, text=label)
        label_widget.pack(side='left', padx=10)

        # Create a frame to show the current color
        color_preview = tk.Frame(color_row, width=30, height=20, bg=f"#{default_color:06x}")
        color_preview.pack(side='left', padx=10)

        # Function to open color picker and update color
        def pick_color():
            color_code = colorchooser.askcolor(initialcolor=f"#{get_config(config_key, default_color):06x}")
            if color_code[1]:  # If a color was selected (not cancelled)
                # Convert from #RRGGBB to 0xRRGGBB
                hex_color = int(color_code[1][1:], 16)
                update_config_value(config_key, hex_color)
                color_preview.config(bg=color_code[1])

        # Button to open color picker
        color_button = ctk.CTkButton(color_row, text="Change", command=pick_color)
        color_button.pack(side='left', padx=10)

    # Create color pickers for different message types
    create_color_picker(color_frame, 0, "Normal Messages:", "runes_text_color", 0x00FF00)
    create_color_picker(color_frame, 1, "Warning Messages:", "warning_text_color", 0xFF0000)
    create_color_picker(color_frame, 2, "Timer Messages:", "timer_text_color", 0xFFD700)

    # Test visual settings button
    test_visuals_button = ctk.CTkButton(visuals_tab, text="Test Visual Settings",
                                     command=lambda: test_visual_settings())
    test_visuals_button.pack(padx=10, pady=15)

    # Function to test visual settings
    def test_visual_settings():
        # Display test messages with current settings
        normal_color = get_config("runes_text_color", 0x00FF00)
        warning_color = get_config("warning_text_color", 0xFF0000)
        timer_color = get_config("timer_text_color", 0xFFD700)

        # Clear any existing test messages
        if test_message_object:
            try:
                test_message_object.set_value("")
            except:
                pass

        # Show test messages
        display_runes_message("Test normal message", normal_color, 5)
        display_runes_message("Test warning message", warning_color, 5)
        display_runes_message("Test timer message", timer_color, 5)

        minescript.echo("§a§lTest messages displayed with current visual settings")

    # Create a frame for message toggles
    message_frame = ctk.CTkFrame(runes_tab)
    message_frame.pack(fill='x', padx=10, pady=10, expand=True)

    # Add a label for the frame
    message_label = ctk.CTkLabel(message_frame, text="Message Settings", font=ctk.CTkFont(size=14, weight="bold"))
    message_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Create a sub-frame for the grid layout
    message_switches_frame = ctk.CTkFrame(message_frame)
    message_switches_frame.pack(fill='x', padx=10, pady=5)

    # Function to create a toggle switch
    def create_toggle_switch(parent, row, col, text, config_key):
        var = tk.BooleanVar(value=get_config(config_key, True))

        def toggle_callback():
            update_config_value(config_key, var.get())

        switch = ctk.CTkSwitch(
            parent,
            text=text,
            variable=var,
            command=toggle_callback,
            onvalue=True,
            offvalue=False
        )
        switch.grid(row=row, column=col, padx=15, pady=5, sticky='w')
        return var, switch

    # Main toggle for all rune messages
    rune_messages_var, _ = create_toggle_switch(
        message_switches_frame, 0, 0, "Show All Rune Messages", "show_rune_messages"
    )

    # Create two columns for switches to save space
    # Left column
    runic_obstruction_var, _ = create_toggle_switch(
        message_switches_frame, 1, 0, "Runic Obstruction", "show_runic_obstruction"
    )

    anc_helm_proc_var, _ = create_toggle_switch(
        message_switches_frame, 2, 0, "Ancient Helm Proc", "show_anc_helm_proc"
    )

    dasher_message_var, _ = create_toggle_switch(
        message_switches_frame, 3, 0, "Dasher Cooldown", "show_dasher_message"
    )

    static_disarm_message_var, _ = create_toggle_switch(
        message_switches_frame, 4, 0, "Static Disarm", "show_static_disarm_message"
    )

    # Right column
    honorable_blade_message_var, _ = create_toggle_switch(
        message_switches_frame, 1, 1, "Imp Sword", "show_honorable_blade_message"
    )

    quantum_chestplate_var, _ = create_toggle_switch(
        message_switches_frame, 2, 1, "Quantum Chestplate", "show_quantum_chestplate_message"
    )

    panda_helmet_var, _ = create_toggle_switch(
        message_switches_frame, 3, 1, "Panda Helmet", "show_panda_helmet_message"
    )

    spooky_helm_var, _ = create_toggle_switch(
        message_switches_frame, 4, 1, "Spooky Helm", "show_spooky_helm_message"
    )

    cupid_boots_var, _ = create_toggle_switch(
        message_switches_frame, 5, 1, "Cupid Boots", "show_cupid_boots_message"
    )

    # Add a reset button for message settings
    def reset_message_settings_callback():
        reset_message_settings(
            [rune_messages_var, runic_obstruction_var, anc_helm_proc_var,
             dasher_message_var, static_disarm_message_var, honorable_blade_message_var,
             quantum_chestplate_var, panda_helmet_var, spooky_helm_var, cupid_boots_var]
        )

    reset_messages_button = ctk.CTkButton(
        message_switches_frame,
        text="Reset All Message Settings",
        command=reset_message_settings_callback
    )
    reset_messages_button.grid(row=6, column=0, columnspan=2, padx=15, pady=15)

    # Function to reset message settings
    def reset_message_settings(var_list):
        for var in var_list:
            var.set(True)

        # Update all config values
        update_config_value("show_rune_messages", True)
        update_config_value("show_runic_obstruction", True)
        update_config_value("show_anc_helm_proc", True)
        update_config_value("show_dasher_message", True)
        update_config_value("show_static_disarm_message", True)
        update_config_value("show_honorable_blade_message", True)
        update_config_value("show_quantum_chestplate_message", True)
        update_config_value("show_panda_helmet_message", True)
        update_config_value("show_spooky_helm_message", True)
        update_config_value("show_cupid_boots_message", True)

        minescript.echo("§a§lAll message settings reset to enabled")

    # ======= Chat Games Tab ========
    # Create frames for organization
    chat_position_frame = ctk.CTkFrame(chat_games_tab)
    chat_position_frame.pack(fill='x', padx=10, pady=10)

    # Add a label for the frame
    chat_pos_label = ctk.CTkLabel(chat_position_frame, text="Position Settings", font=ctk.CTkFont(size=14, weight="bold"))
    chat_pos_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Create a sub-frame for the grid layout
    chat_pos_controls_frame = ctk.CTkFrame(chat_position_frame)
    chat_pos_controls_frame.pack(fill='x', padx=10, pady=5)

    # Create a grid layout for position controls
    chat_x_label = ctk.CTkLabel(chat_pos_controls_frame, text="Chat Game X Position:")
    chat_x_label.grid(row=0, column=0, padx=10, pady=5, sticky='w')

    chat_game_x_value = ctk.CTkLabel(chat_pos_controls_frame, text=f"{get_config('chat_game_x', 47)}")
    chat_game_x_value.grid(row=0, column=1, padx=10, pady=5)

    def update_chat_x_pos(value):
        int_value = int(float(value))
        update_config_value("chat_game_x", int_value)
        chat_game_x_value.configure(text=f"{int_value}")

    chat_game_x_slider = ctk.CTkSlider(chat_pos_controls_frame, from_=0, to=500, width=300,
                                     command=update_chat_x_pos)
    chat_game_x_slider.set(get_config("chat_game_x", 47))
    chat_game_x_slider.grid(row=1, column=0, columnspan=2, padx=10, pady=5, sticky='ew')

    chat_y_label = ctk.CTkLabel(chat_pos_controls_frame, text="Chat Game Y Position:")
    chat_y_label.grid(row=2, column=0, padx=10, pady=5, sticky='w')

    chat_game_y_value = ctk.CTkLabel(chat_pos_controls_frame, text=f"{get_config('chat_game_y', 15)}")
    chat_game_y_value.grid(row=2, column=1, padx=10, pady=5)

    def update_chat_y_pos(value):
        int_value = int(float(value))
        update_config_value("chat_game_y", int_value)
        chat_game_y_value.configure(text=f"{int_value}")

    chat_game_y_slider = ctk.CTkSlider(chat_pos_controls_frame, from_=0, to=200, width=300,
                                     command=update_chat_y_pos)
    chat_game_y_slider.set(get_config("chat_game_y", 15))
    chat_game_y_slider.grid(row=3, column=0, columnspan=2, padx=10, pady=5, sticky='ew')

    # Test button for position
    chat_test_button = ctk.CTkButton(chat_pos_controls_frame, text="Test Position", command=toggle_chat_game_test_message)
    chat_test_button.grid(row=4, column=0, columnspan=2, padx=10, pady=10)

    # Create a frame for chat game settings
    chat_settings_frame = ctk.CTkFrame(chat_games_tab)
    chat_settings_frame.pack(fill='x', padx=10, pady=10, expand=True)

    # Add a label for the frame
    chat_settings_label = ctk.CTkLabel(chat_settings_frame, text="Chat Game Settings", font=ctk.CTkFont(size=14, weight="bold"))
    chat_settings_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Toggle for chat game messages
    chat_game_messages_var = tk.BooleanVar(value=get_config("show_chat_game_message", True))
    chat_game_messages_switch = ctk.CTkSwitch(
        chat_settings_frame,
        text="Show Chat Game Messages",
        variable=chat_game_messages_var,
        command=lambda: update_config_value("show_chat_game_message", chat_game_messages_var.get())
    )
    chat_game_messages_switch.pack(padx=15, pady=10, anchor='w')

    # Add a timer duration setting
    timer_label = ctk.CTkLabel(chat_settings_frame, text="Chat Game Timer Duration:")
    timer_label.pack(padx=15, pady=(15, 5), anchor='w')

    timer_frame = ctk.CTkFrame(chat_settings_frame)
    timer_frame.pack(fill='x', padx=15, pady=5)

    timer_values = [180, 240, 300, 360, 420]  # 3, 4, 5, 6, 7 minutes

    # Create a segmented button for timer selection
    timer_segments = []
    for val in timer_values:
        minutes = val // 60
        timer_segments.append(f"{minutes} min")

    def timer_callback(value):
        # Get the index of the selected segment
        index = timer_segments.index(value)
        # Set the timer duration
        set_chat_game_timer_duration(timer_values[index])

    timer_selector = ctk.CTkSegmentedButton(
        timer_frame,
        values=timer_segments,
        command=timer_callback
    )
    timer_selector.pack(pady=10)

    # Set the initial selection
    current_index = timer_values.index(chat_game_timer_duration) if chat_game_timer_duration in timer_values else 2
    timer_selector.set(timer_segments[current_index])

    # Add a button to manually start the chat game timer
    start_timer_button = ctk.CTkButton(
        chat_settings_frame,
        text="Start Chat Game Timer Now",
        command=start_chat_game_timer
    )
    start_timer_button.pack(padx=15, pady=15)

    # ======= Item Creator Tab ========
    # Use the imported function to create the Item Creator tab
    create_item_creator_tab(tabview, ITEM_CONFIGS, save_items)

    # About tab
    about_text = f"""
    PVP Utils - Enhanced Edition v{SCRIPT_VERSION}

    Original by Cerv 😈
    Discord: itscerv
    Enhanced by Augment AI

    This utility helps track various cooldowns and events in PVP:
    - Rune effects and cooldowns with enhanced visuals
    - Chat game timers with countdown display
    - Various item cooldowns with customizable appearance
    - Improved GUI with more configuration options

    Features:
    • Enhanced text rendering with shadows and outlines
    • Customizable colors and text appearance
    • Improved configuration system
    • Better error handling and compatibility
    """

    # Create a frame for the about content
    about_frame = ctk.CTkFrame(about_tab)
    about_frame.pack(fill='both', expand=True, padx=20, pady=20)

    # Add the about text
    about_label = ctk.CTkLabel(about_frame, text=about_text, justify='center', wraplength=450)
    about_label.pack(expand=True, padx=20, pady=20)

    # Add buttons for additional actions
    buttons_frame = ctk.CTkFrame(about_tab)
    buttons_frame.pack(fill='x', padx=20, pady=10)

    # Reset all settings button
    reset_all_button = ctk.CTkButton(
        buttons_frame,
        text="Reset All Settings",
        command=reset_config
    )
    reset_all_button.pack(side='left', padx=20, pady=10)

    # Clear all messages button
    clear_messages_button = ctk.CTkButton(
        buttons_frame,
        text="Clear All Messages",
        command=clear_all_messages
    )
    clear_messages_button.pack(side='left', padx=20, pady=10)

    # Add a status bar
    status_frame = ctk.CTkFrame(root)
    status_frame.pack(fill='x', side='bottom')
    status_label = ctk.CTkLabel(status_frame, text=f"PVP Utils v{SCRIPT_VERSION} - Settings saved automatically")
    status_label.pack(fill='x', padx=10, pady=5)

    root.mainloop()

# Function to set chat game timer duration
def set_chat_game_timer_duration(duration):
    """Set the duration for the chat game timer."""
    global chat_game_timer_duration
    chat_game_timer_duration = duration
    # No need to save to config as this is a session setting

def toggle_chat_game_test_message():
    """Toggle the display of a test message for chat game position."""
    global test_message_displayed, test_message_object, DRAW_TEXT_AVAILABLE

    if test_message_displayed:
        # Remove the test message if it's displayed
        if test_message_object and test_message_object in chat_game_text_objects:
            chat_game_text_objects.remove(test_message_object)
        if test_message_object:  # Only clear the text if the object exists
            test_message_object.string.set_value("")  # Clear the text
        test_message_displayed = False
        minescript.echo("§7Chat game test message removed")
    else:
        # Show the test message if it's not displayed
        if DRAW_TEXT_AVAILABLE:
            minescript.echo("§a§lTest Message: §r§aDisplaying a chat game test message")
            test_message_object = display_chat_game_message("Test Chat Game message at position", 0x00ff00, 5)
            test_message_displayed = True
        else:
            minescript.echo("§e§lTest Message: §r§eDisplaying a chat game test message in chat (on-screen display unavailable)")
            test_message_object = display_chat_game_message("Test Chat Game message at position", 0x00ff00, 5)
            test_message_displayed = True

def update_chat_game_x(value):
    global chat_game_x
    chat_game_x = int(float(value))
    save_config()

def update_chat_game_y(value):
    global chat_game_y
    chat_game_y = int(float(value))
    save_config()

# Function to run the GUI in a separate thread
def run_gui():
    """Run the GUI in a separate thread."""
    gui_thread = threading.Thread(target=create_gui)
    gui_thread.daemon = True
    gui_thread.start()

# Function to clear all displayed messages
def clear_all_messages():
    """Clear all displayed messages from memory."""
    try:
        # Clear the lists of text objects
        if runes_chat_text_objects:
            # Set all text values to empty string first
            for text_obj in runes_chat_text_objects:
                if text_obj:
                    try:
                        text_obj.set_value("")
                    except:
                        pass  # Ignore errors

            # Then clear the list
            runes_chat_text_objects.clear()

        if chat_game_text_objects:
            # Set all text values to empty string first
            for text_obj in chat_game_text_objects:
                if text_obj:
                    try:
                        text_obj.set_value("")
                    except:
                        pass  # Ignore errors

            # Then clear the list
            chat_game_text_objects.clear()

        # Let the user know messages were cleared
        minescript.echo("§7Messages cleared")

    except Exception as e:
        print(f"Error clearing messages: {e}")
        # Don't raise the exception - this is a cleanup function

# Main function
def main():
    """Continuously monitor chat for updates, update the countdown, and display the filtered messages.
    This is the enhanced version with improved visuals, better GUI, and more features.
    """
    try:
        # Show initialization message using minescript.echo only
        minescript.echo("§9§l╔═══════════════════════════════════════╗")
        minescript.echo(f"§9§l║  §a§lPVP Utils §f§l- §e§lEnhanced Edition §6§lv{SCRIPT_VERSION}  §9§l║")
        minescript.echo("§9§l╚═══════════════════════════════════════╝")
        minescript.echo("§7Type §f/gui §7to open settings")
        minescript.echo("§7Enhanced by §fAugment AI §7with improved visuals and features")

        # Initialize the script in the correct order
        read_config()  # Read config at startup first
        load_items()   # Load item configurations

        # Initialize global variables
        global current_chat_game_message, chat_game_messages, chat_game_text_objects
        global runes_chat_messages, runes_chat_text_objects, DRAW_TEXT_AVAILABLE
        global global_draw_centered_string

        # Ensure lists are properly initialized
        runes_chat_messages = []
        runes_chat_text_objects = []
        chat_game_messages = []
        chat_game_text_objects = []

        # Start the GUI in a separate thread
        run_gui()

        # Set up initial chat game message
        current_chat_game_message = "Waiting for a chat game to start"

        # Initialize our text display system
        try:
            # Try to create a test message to initialize the system
            test_obj = safe_draw_centered_string("Initializing...", x=250, y=10, color=0xFFFFFF, scale=1.0)

            # Check if we're using the real function or fallback
            if DRAW_TEXT_AVAILABLE:
                minescript.echo("§a§lPVP Utils is now running with on-screen display")
                if USE_EFFECTS:
                    minescript.echo("§a§lEnhanced text effects are enabled")
            else:
                minescript.echo("§e§lPVP Utils is running in chat display mode")
                minescript.echo("§e§lOn-screen display is not available")

            # Clear the test message
            if test_obj:
                test_obj.set_value("")
        except Exception as e:
            # If there's an error, switch to fallback mode
            DRAW_TEXT_AVAILABLE = False
            print(f"Error initializing text display: {e}")
            minescript.echo("§e§lPVP Utils is running in chat display mode")
            minescript.echo(f"§c§lError: {e}")
            traceback.print_exc()

        # Display the initial message
        display_chat_game_message(current_chat_game_message, 0xffd700, 0)

    except Exception as e:
        print(f"Error during initialization: {e}")
        minescript.echo(f"§c§lError during initialization: §r§7{e}")
        # Continue anyway to give the script a chance to work

    # Main event loop
    try:
        with EventQueue() as event_queue:
            event_queue.register_chat_listener()

            while True:
                try:
                    # Process chat events
                    event = event_queue.get(timeout=0.1)
                    if event and event.type == EventType.CHAT:
                        try:
                            on_chat_message(event.message)
                        except Exception as e:
                            print(f"Error processing chat message: {e}")
                            minescript.echo(f"§c§lError processing message: §r§7{e}")
                except queue.Empty:
                    pass
                except Exception as e:
                    print(f"Error in event loop: {e}")
                    minescript.echo(f"§c§lError: §r§7{e}")  # Show error in-game with color

                # Update message countdowns with error handling
                try:
                    # Update the runes messages
                    update_runes_messages()
                except Exception as e:
                    print(f"Error updating runes messages: {e}")

                try:
                    # Update the chat game messages
                    update_chat_game_messages()
                except Exception as e:
                    print(f"Error updating chat game messages: {e}")

                # Throttle the updates to reduce load
                time.sleep(0.1)  # Add a delay to prevent overwhelming Minecraft
    except Exception as e:
        print(f"Fatal error in main loop: {e}")
        minescript.echo(f"§c§lFatal error: §r§7{e}")

if __name__ == "__main__":
    main()