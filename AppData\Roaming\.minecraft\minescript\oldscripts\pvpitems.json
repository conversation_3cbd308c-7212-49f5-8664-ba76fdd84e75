{"honorable_blade": {"display_name": "Imp Sword", "config_key": "show_honorable_blade_message", "pattern": "RUNES.*You have dashed forward using your Honorable Blade (.*)!", "message_template": "You can use Imp Sword again in {time_display}", "cooldowns": {"I": 60, "II": 55, "III": 50, "IV": 45, "V": 40}, "color": 65280, "extract_tier": true}, "ancient_helm": {"display_name": "Ancient Helm", "config_key": "show_anc_helm_proc", "pattern": "RUNES.*(.*) has a combo of 3 on you! Your Ra's Wrath (IV|V) has activated!", "negative_pattern": "RUNES.*Your combo of 3 on (.*) has activated their Ra's Wrath (IV|V)!", "message_template": "Ancient <PERSON><PERSON> procced on {player} for {time_display}", "negative_message_template": "{player}'s <PERSON> procced on you for {time_display}", "durations": {"IV": 7, "V": 9}, "color": 65280, "negative_color": 16711680, "extract_tier": true}, "quantum_chestplate": {"display_name": "Quantum Chestplate", "config_key": "show_quantum_chestplate_message", "pattern": "RUNES.*You activated Rewind (I|II|III|IV|V)! Lets try that again!", "message_template": "You can use Quantum Chestplate {tier} again in {time_display}", "cooldowns": {"I": 540, "II": 480, "III": 420, "IV": 360, "V": 300}, "color": 16766720, "extract_tier": true}, "panda_helmet": {"display_name": "<PERSON><PERSON> Helm<PERSON>", "config_key": "show_panda_helmet_message", "pattern": "RUNES.*Your Sneeze (V|IV|III|II|I)", "message_template": "You can use Panda Helmet in {time_display}", "cooldown": 120, "color": 16766720, "extract_tier": false}, "spooky_helm": {"display_name": "Spooky Helm", "config_key": "show_spooky_helm_message", "pattern": "RUNES.*Your Illusion (V|IV|III|II|I)", "message_template": "You can use Spooky Helm in {time_display}", "cooldown": 180, "color": 16766720, "extract_tier": false}, "cupid_boots": {"display_name": "<PERSON><PERSON>", "config_key": "show_cupid_boots_message", "pattern": "RUNES.*Your Intoxicating Love (V|IV|III|II|I)", "message_template": "You can use Cupid Boots in {time_display}", "cooldown": 90, "color": 16766720, "extract_tier": false}, "runic_obstruction": {"display_name": "Runic Obstruction", "config_key": "show_runic_obstruction", "pattern": "RUNES.*You have disabled the rune effects of (.*) with your Runic Obstruction .* for (\\d+) seconds.", "negative_pattern": "RUNES.*(.*) has prevented your custom enchants from working for (\\d+) seconds with their Runic Obstruction .*.", "message_template": "{player} is RUNICED for {duration} seconds", "negative_message_template": "You are RUNICED for {duration} seconds", "color": 65280, "negative_color": 16711680, "extract_duration": true}, "dasher": {"display_name": "<PERSON><PERSON>", "config_key": "show_dasher_message", "pattern": "RUNES.*Dashed! Your Dasher (.*) is usable again in: (\\d+)s", "message_template": "You can use <PERSON><PERSON> again in {duration} seconds", "color": 65280, "extract_duration": true}, "static_disarm": {"display_name": "Static Disarm", "config_key": "show_static_disarm_message", "pattern": "RUNES.*(.*)'s Static Disarm (.*) reduced your attack speed by (\\d+)% for (\\d+) seconds", "message_template": "{player} reduced your attack speed by {percent}% for {duration} seconds", "color": 16711680, "extract_duration": true}}