"""
Item Creator Tab for PVP Utils

This module contains the code for the Item Creator tab in the PVP Utils GUI.
It allows users to create and manage custom item configurations.
"""

import tkinter as tk
from tkinter import messagebox, colorchooser
import customtkinter as ctk
import json
import os

# Path to the items configuration file
ITEMS_FILE_PATH = os.path.join("config", "pvpitems.json")

# Global reference to the main script's ITEM_CONFIGS
ITEM_CONFIGS = {}

def create_item_creator_tab(tabview, item_configs=None, save_callback=None):
    """Create the Item Creator tab in the GUI.

    Args:
        tabview: The CustomTkinter tabview to add the tab to
        item_configs: Reference to the main script's ITEM_CONFIGS dictionary
        save_callback: Function to call to save the item configurations
    """
    global ITEM_CONFIGS

    # Store reference to the main script's ITEM_CONFIGS if provided
    if item_configs is not None:
        ITEM_CONFIGS = item_configs

    # Create the tab
    item_creator_tab = tabview.add("Add New Item")

    # Create a frame for the form
    new_item_frame = ctk.CTkFrame(item_creator_tab)
    new_item_frame.pack(fill='both', expand=True, padx=20, pady=20)

    # Add a title
    title_label = ctk.CTkLabel(
        new_item_frame,
        text="Create New Item Configuration",
        font=ctk.CTkFont(size=16, weight="bold")
    )
    title_label.pack(pady=(0, 20))

    # Create a scrollable frame for the form fields
    form_frame = ctk.CTkScrollableFrame(new_item_frame)
    form_frame.pack(fill='both', expand=True, padx=10, pady=10)

    # Basic Information section
    basic_info_frame = ctk.CTkFrame(form_frame)
    basic_info_frame.pack(fill='x', padx=10, pady=10)

    basic_info_label = ctk.CTkLabel(
        basic_info_frame,
        text="Basic Information",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    basic_info_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Item ID
    item_id_frame = ctk.CTkFrame(basic_info_frame)
    item_id_frame.pack(fill='x', padx=10, pady=5)

    item_id_label = ctk.CTkLabel(item_id_frame, text="Item ID:")
    item_id_label.pack(side='left', padx=10)

    item_id_var = tk.StringVar()
    item_id_entry = ctk.CTkEntry(item_id_frame, textvariable=item_id_var, width=200)
    item_id_entry.pack(side='left', padx=10)

    item_id_help = ctk.CTkLabel(
        item_id_frame,
        text="(lowercase, no spaces)",
        text_color="gray"
    )
    item_id_help.pack(side='left', padx=10)

    # Display Name
    display_name_frame = ctk.CTkFrame(basic_info_frame)
    display_name_frame.pack(fill='x', padx=10, pady=5)

    display_name_label = ctk.CTkLabel(display_name_frame, text="Display Name:")
    display_name_label.pack(side='left', padx=10)

    display_name_var = tk.StringVar()
    display_name_entry = ctk.CTkEntry(display_name_frame, textvariable=display_name_var, width=200)
    display_name_entry.pack(side='left', padx=10)

    # Pattern section
    pattern_frame = ctk.CTkFrame(form_frame)
    pattern_frame.pack(fill='x', padx=10, pady=10)

    pattern_label = ctk.CTkLabel(
        pattern_frame,
        text="Pattern and Message",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    pattern_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Chat Pattern
    chat_pattern_frame = ctk.CTkFrame(pattern_frame)
    chat_pattern_frame.pack(fill='x', padx=10, pady=5)

    chat_pattern_label = ctk.CTkLabel(chat_pattern_frame, text="Chat Pattern (regex):")
    chat_pattern_label.pack(side='left', padx=10)

    pattern_var = tk.StringVar()
    pattern_entry = ctk.CTkEntry(chat_pattern_frame, textvariable=pattern_var, width=300)
    pattern_entry.pack(side='left', padx=10)

    # Message Template
    message_template_frame = ctk.CTkFrame(pattern_frame)
    message_template_frame.pack(fill='x', padx=10, pady=5)

    message_template_label = ctk.CTkLabel(message_template_frame, text="Message Template:")
    message_template_label.pack(side='left', padx=10)

    message_template_var = tk.StringVar()
    message_template_entry = ctk.CTkEntry(message_template_frame, textvariable=message_template_var, width=300)
    message_template_entry.pack(side='left', padx=10)

    # Cooldown section
    cooldown_frame = ctk.CTkFrame(form_frame)
    cooldown_frame.pack(fill='x', padx=10, pady=10)

    cooldown_label = ctk.CTkLabel(
        cooldown_frame,
        text="Cooldown Settings",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    cooldown_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Cooldown type selection
    cooldown_type_frame = ctk.CTkFrame(cooldown_frame)
    cooldown_type_frame.pack(fill='x', padx=10, pady=5)

    cooldown_type_var = tk.StringVar(value="fixed")

    fixed_radio = ctk.CTkRadioButton(
        cooldown_type_frame,
        text="Fixed Cooldown",
        variable=cooldown_type_var,
        value="fixed"
    )
    fixed_radio.pack(side='left', padx=20)

    tier_radio = ctk.CTkRadioButton(
        cooldown_type_frame,
        text="Tier-Based Cooldown",
        variable=cooldown_type_var,
        value="tier"
    )
    tier_radio.pack(side='left', padx=20)

    # Fixed cooldown
    fixed_cooldown_frame = ctk.CTkFrame(cooldown_frame)
    fixed_cooldown_frame.pack(fill='x', padx=10, pady=5)

    fixed_cooldown_label = ctk.CTkLabel(fixed_cooldown_frame, text="Fixed Cooldown (seconds):")
    fixed_cooldown_label.pack(side='left', padx=10)

    fixed_cooldown_var = tk.IntVar(value=60)
    fixed_cooldown_entry = ctk.CTkEntry(fixed_cooldown_frame, textvariable=fixed_cooldown_var, width=100)
    fixed_cooldown_entry.pack(side='left', padx=10)

    # Tier-based cooldown
    tier_cooldown_frame = ctk.CTkFrame(cooldown_frame)
    tier_cooldown_frame.pack(fill='x', padx=10, pady=5)

    # Number of tiers
    num_tiers_label = ctk.CTkLabel(tier_cooldown_frame, text="Number of Tiers:")
    num_tiers_label.pack(side='left', padx=10)

    num_tiers_var = tk.IntVar(value=5)
    num_tiers_values = ["1", "2", "3", "4", "5"]
    num_tiers_dropdown = ctk.CTkOptionMenu(
        tier_cooldown_frame,
        values=num_tiers_values,
        command=lambda value: num_tiers_var.set(int(value))
    )
    num_tiers_dropdown.set("5")
    num_tiers_dropdown.pack(side='left', padx=10)

    # Tier cooldowns
    tier_values_frame = ctk.CTkFrame(cooldown_frame)
    tier_values_frame.pack(fill='x', padx=10, pady=5)

    # Create entries for each tier
    tier_vars = {}
    tier_entries = {}

    for i, tier in enumerate(["I", "II", "III", "IV", "V"]):
        tier_frame = ctk.CTkFrame(tier_values_frame)
        tier_frame.pack(side='left', padx=10)

        tier_label = ctk.CTkLabel(tier_frame, text=f"Tier {tier}:")
        tier_label.pack(pady=2)

        tier_vars[tier] = tk.IntVar(value=60 - i*5)  # Default values decreasing by tier
        tier_entries[tier] = ctk.CTkEntry(tier_frame, textvariable=tier_vars[tier], width=50)
        tier_entries[tier].pack(pady=2)

    # Options section
    options_frame = ctk.CTkFrame(form_frame)
    options_frame.pack(fill='x', padx=10, pady=10)

    options_label = ctk.CTkLabel(
        options_frame,
        text="Options",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    options_label.pack(anchor="w", padx=10, pady=(10, 5))

    # Extract tier option
    extract_tier_var = tk.BooleanVar(value=False)
    extract_tier_switch = ctk.CTkSwitch(
        options_frame,
        text="Extract Tier from Pattern",
        variable=extract_tier_var
    )
    extract_tier_switch.pack(anchor='w', padx=20, pady=5)

    # Extract duration option
    extract_duration_var = tk.BooleanVar(value=False)
    extract_duration_switch = ctk.CTkSwitch(
        options_frame,
        text="Extract Duration from Pattern",
        variable=extract_duration_var
    )
    extract_duration_switch.pack(anchor='w', padx=20, pady=5)

    # Note about tier extraction
    tier_note = ctk.CTkLabel(
        options_frame,
        text="Note: Extract Tier is required for tier-based cooldowns",
        text_color="blue"
    )
    tier_note.pack(anchor='w', padx=20, pady=5)

    # Color picker
    color_frame = ctk.CTkFrame(form_frame)
    color_frame.pack(fill='x', padx=10, pady=10)

    color_label = ctk.CTkLabel(
        color_frame,
        text="Text Color",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    color_label.pack(anchor="w", padx=10, pady=(10, 5))

    color_picker_frame = ctk.CTkFrame(color_frame)
    color_picker_frame.pack(fill='x', padx=10, pady=5)

    color_var = tk.IntVar(value=0x00FF00)  # Default green

    # Create a frame to show the current color
    color_preview = tk.Frame(color_picker_frame, width=30, height=20, bg=f"#{color_var.get():06x}")
    color_preview.pack(side='left', padx=10)

    def pick_color():
        color_code = colorchooser.askcolor(initialcolor=f"#{color_var.get():06x}")
        if color_code[1]:  # If a color was selected (not cancelled)
            # Convert from #RRGGBB to 0xRRGGBB
            hex_color = int(color_code[1][1:], 16)
            color_var.set(hex_color)
            color_preview.config(bg=color_code[1])

    color_button = ctk.CTkButton(color_picker_frame, text="Choose Color", command=pick_color)
    color_button.pack(side='left', padx=10)

    # Function to update UI based on cooldown type
    def update_cooldown_ui():
        cooldown_type = cooldown_type_var.get()
        if cooldown_type == "fixed":
            # Show fixed cooldown, hide tier settings
            fixed_cooldown_frame.pack(fill='x', padx=10, pady=5)
            tier_cooldown_frame.pack_forget()
            tier_values_frame.pack_forget()
        else:
            # Hide fixed cooldown, show tier settings
            fixed_cooldown_frame.pack_forget()
            tier_cooldown_frame.pack(fill='x', padx=10, pady=5)
            tier_values_frame.pack(fill='x', padx=10, pady=5)

    # Bind the radio buttons to update the UI
    fixed_radio.configure(command=update_cooldown_ui)
    tier_radio.configure(command=update_cooldown_ui)

    # Function to update UI based on extract tier option
    def update_extract_tier_ui():
        if extract_tier_var.get():
            # If extracting tier, enable tier-based cooldown
            tier_radio.configure(state="normal")
        else:
            # If not extracting tier, force fixed cooldown and disable tier-based
            fixed_radio.select()
            tier_radio.configure(state="disabled")
            update_cooldown_ui()

    # Bind the extract tier switch to update the UI
    extract_tier_switch.configure(command=update_extract_tier_ui)

    # Create button
    def create_new_item():
        # Get values from form
        item_id = item_id_var.get().strip()
        display_name = display_name_var.get().strip()
        pattern = pattern_var.get().strip()
        message_template = message_template_var.get().strip()
        color = color_var.get()
        extract_tier = extract_tier_var.get()
        extract_duration = extract_duration_var.get()

        # Validate inputs
        if not item_id or not display_name or not pattern or not message_template:
            messagebox.showerror("Error", "All fields are required")
            return

        # Validate that Extract Tier is enabled when using tier-based cooldowns
        if cooldown_type_var.get() == "tier" and not extract_tier_var.get():
            messagebox.showerror("Error", "Extract Tier from Pattern must be enabled when using tier-based cooldowns")
            return

        # Create config key
        config_key = f"show_{item_id}_message"

        # Create the item config
        new_item = {
            "display_name": display_name,
            "config_key": config_key,
            "pattern": pattern,
            "message_template": message_template,
            "color": color,
            "extract_tier": extract_tier,
            "extract_duration": extract_duration
        }

        # Add cooldown information
        if cooldown_type_var.get() == "fixed":
            new_item["cooldown"] = fixed_cooldown_var.get()
        else:
            cooldowns = {}
            num_tiers = num_tiers_var.get()
            all_tiers = ["I", "II", "III", "IV", "V"]

            # Only include the selected number of tiers
            for i, tier in enumerate(all_tiers):
                if i < num_tiers:
                    cooldowns[tier] = tier_vars[tier].get()

            new_item["cooldowns"] = cooldowns

        # Add to ITEM_CONFIGS
        ITEM_CONFIGS[item_id] = new_item

        # Save the item configurations
        if save_callback:
            save_callback()
        else:
            # Default save implementation
            try:
                # Ensure the directory exists
                items_dir = os.path.dirname(ITEMS_FILE_PATH)
                os.makedirs(items_dir, exist_ok=True)

                # Save the items as JSON
                with open(ITEMS_FILE_PATH, 'w', encoding='utf-8') as file:
                    json.dump(ITEM_CONFIGS, file, indent=4)

                messagebox.showinfo("Success", "Item configurations saved successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Error saving item configurations: {e}")

        # Show success message
        messagebox.showinfo("Success", f"Item '{display_name}' added successfully!")

        # Clear form
        item_id_var.set("")
        display_name_var.set("")
        pattern_var.set("")
        message_template_var.set("")
        fixed_cooldown_var.set(60)
        for var in tier_vars.values():
            var.set(60)
        extract_tier_var.set(False)
        extract_duration_var.set(False)
        update_extract_tier_ui()

    # Add the create button
    create_button = ctk.CTkButton(
        new_item_frame,
        text="Create Item",
        command=create_new_item
    )
    create_button.pack(pady=20)

    # Add a button to view and manage existing items
    manage_items_frame = ctk.CTkFrame(item_creator_tab)
    manage_items_frame.pack(fill='x', padx=20, pady=10)

    manage_items_label = ctk.CTkLabel(
        manage_items_frame,
        text="Manage Existing Items",
        font=ctk.CTkFont(size=14, weight="bold")
    )
    manage_items_label.pack(anchor="w", padx=10, pady=5)

    def show_items_manager():
        # Create a new window to display and manage items
        items_window = tk.Toplevel()
        items_window.title("Item Manager")
        items_window.geometry("700x500")

        # Create a frame for the list of items
        items_frame = ctk.CTkFrame(items_window)
        items_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Create a label for the list
        items_label = ctk.CTkLabel(
            items_frame,
            text="Existing Items",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        items_label.pack(anchor="w", padx=10, pady=5)

        # Create a frame for the listbox with proper background
        listbox_frame = ctk.CTkFrame(items_frame)
        listbox_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create a listbox to display items with proper colors
        items_listbox = tk.Listbox(
            listbox_frame,
            width=50,
            height=10,
            bg="#333333",  # Dark background
            fg="#FFFFFF",  # White text
            selectbackground="#1F538D",  # Blue selection background
            selectforeground="#FFFFFF",  # White selection text
            font=("Arial", 10)
        )
        items_listbox.pack(fill='both', expand=True, side='left')

        # Add a scrollbar
        scrollbar = tk.Scrollbar(listbox_frame, command=items_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        items_listbox.config(yscrollcommand=scrollbar.set)

        # Populate the listbox with items
        for item_id, item_config in ITEM_CONFIGS.items():
            display_name = item_config.get("display_name", item_id)
            items_listbox.insert(tk.END, f"{display_name} ({item_id})")

        # Create a frame for the details
        details_frame = ctk.CTkFrame(items_window)
        details_frame.pack(fill='x', padx=20, pady=10)

        # Create a text widget to display item details with proper colors
        details_text = tk.Text(
            details_frame,
            width=60,
            height=10,
            wrap='word',
            bg="#333333",  # Dark background
            fg="#FFFFFF",  # White text
            insertbackground="#FFFFFF",  # White cursor
            font=("Arial", 10)
        )
        details_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Function to display item details
        def show_item_details(_=None):  # _ to indicate unused parameter
            selection = items_listbox.curselection()
            if selection:
                # Get the selected item
                item_text = items_listbox.get(selection[0])
                item_id = item_text.split("(")[1].split(")")[0]

                # Get the item config
                item_config = ITEM_CONFIGS.get(item_id)
                if item_config:
                    # Format the details
                    details = f"Item ID: {item_id}\n"
                    details += f"Display Name: {item_config.get('display_name', item_id)}\n"
                    details += f"Pattern: {item_config.get('pattern', 'N/A')}\n"
                    details += f"Message Template: {item_config.get('message_template', 'N/A')}\n"

                    if "cooldown" in item_config:
                        details += f"Cooldown: {item_config['cooldown']} seconds\n"
                    elif "cooldowns" in item_config:
                        details += "Tier-based Cooldowns:\n"
                        for tier, cooldown in item_config["cooldowns"].items():
                            details += f"  - Tier {tier}: {cooldown} seconds\n"

                    details += f"Extract Tier: {item_config.get('extract_tier', False)}\n"
                    details += f"Extract Duration: {item_config.get('extract_duration', False)}\n"
                    details += f"Color: #{item_config.get('color', 0x00FF00):06x}\n"

                    # Clear and update the text widget
                    details_text.delete(1.0, tk.END)
                    details_text.insert(tk.END, details)

                    return item_id
            return None

        # Bind the listbox selection event
        items_listbox.bind('<<ListboxSelect>>', show_item_details)

        # Create a frame for the buttons
        buttons_frame = ctk.CTkFrame(items_window)
        buttons_frame.pack(fill='x', padx=20, pady=10)

        # Function to delete an item
        def delete_item():
            selection = items_listbox.curselection()
            if selection:
                # Get the selected item
                item_text = items_listbox.get(selection[0])
                item_id = item_text.split("(")[1].split(")")[0]

                # Confirm deletion
                if messagebox.askyesno("Confirm Deletion", f"Are you sure you want to delete {item_id}?"):
                    # Remove from ITEM_CONFIGS
                    if item_id in ITEM_CONFIGS:
                        del ITEM_CONFIGS[item_id]

                    # Save the item configurations
                    if save_callback:
                        save_callback()

                    # Update the listbox
                    items_listbox.delete(selection[0])

                    # Clear the details
                    details_text.delete(1.0, tk.END)

                    messagebox.showinfo("Success", f"Item {item_id} deleted successfully!")

        # Function to edit an item
        def edit_item():
            item_id = show_item_details()
            if not item_id or item_id not in ITEM_CONFIGS:
                messagebox.showerror("Error", "Please select an item to edit")
                return

            # Get the item config
            item_config = ITEM_CONFIGS[item_id]

            # Create a new window for editing
            edit_window = tk.Toplevel(items_window)
            edit_window.title(f"Edit Item: {item_config.get('display_name', item_id)}")
            edit_window.geometry("700x600")

            # Create a frame for the form
            edit_frame = ctk.CTkScrollableFrame(edit_window)
            edit_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # Basic Information section
            basic_info_frame = ctk.CTkFrame(edit_frame)
            basic_info_frame.pack(fill='x', padx=10, pady=10)

            basic_info_label = ctk.CTkLabel(
                basic_info_frame,
                text="Basic Information",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            basic_info_label.pack(anchor="w", padx=10, pady=(10, 5))

            # Item ID (read-only)
            item_id_frame = ctk.CTkFrame(basic_info_frame)
            item_id_frame.pack(fill='x', padx=10, pady=5)

            item_id_label = ctk.CTkLabel(item_id_frame, text="Item ID:")
            item_id_label.pack(side='left', padx=10)

            item_id_value = ctk.CTkLabel(item_id_frame, text=item_id)
            item_id_value.pack(side='left', padx=10)

            # Display Name
            display_name_frame = ctk.CTkFrame(basic_info_frame)
            display_name_frame.pack(fill='x', padx=10, pady=5)

            display_name_label = ctk.CTkLabel(display_name_frame, text="Display Name:")
            display_name_label.pack(side='left', padx=10)

            display_name_var = tk.StringVar(value=item_config.get('display_name', item_id))
            display_name_entry = ctk.CTkEntry(display_name_frame, textvariable=display_name_var, width=200)
            display_name_entry.pack(side='left', padx=10)

            # Pattern section
            pattern_frame = ctk.CTkFrame(edit_frame)
            pattern_frame.pack(fill='x', padx=10, pady=10)

            pattern_label = ctk.CTkLabel(
                pattern_frame,
                text="Pattern and Message",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            pattern_label.pack(anchor="w", padx=10, pady=(10, 5))

            # Chat Pattern
            chat_pattern_frame = ctk.CTkFrame(pattern_frame)
            chat_pattern_frame.pack(fill='x', padx=10, pady=5)

            chat_pattern_label = ctk.CTkLabel(chat_pattern_frame, text="Chat Pattern (regex):")
            chat_pattern_label.pack(side='left', padx=10)

            pattern_var = tk.StringVar(value=item_config.get('pattern', ''))
            pattern_entry = ctk.CTkEntry(chat_pattern_frame, textvariable=pattern_var, width=300)
            pattern_entry.pack(side='left', padx=10)

            # Message Template
            message_template_frame = ctk.CTkFrame(pattern_frame)
            message_template_frame.pack(fill='x', padx=10, pady=5)

            message_template_label = ctk.CTkLabel(message_template_frame, text="Message Template:")
            message_template_label.pack(side='left', padx=10)

            message_template_var = tk.StringVar(value=item_config.get('message_template', ''))
            message_template_entry = ctk.CTkEntry(message_template_frame, textvariable=message_template_var, width=300)
            message_template_entry.pack(side='left', padx=10)

            # Cooldown section
            cooldown_frame = ctk.CTkFrame(edit_frame)
            cooldown_frame.pack(fill='x', padx=10, pady=10)

            cooldown_label = ctk.CTkLabel(
                cooldown_frame,
                text="Cooldown Settings",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            cooldown_label.pack(anchor="w", padx=10, pady=(10, 5))

            # Cooldown type selection
            cooldown_type_frame = ctk.CTkFrame(cooldown_frame)
            cooldown_type_frame.pack(fill='x', padx=10, pady=5)

            # Determine if using fixed or tier-based cooldown
            has_fixed_cooldown = 'cooldown' in item_config

            cooldown_type_var = tk.StringVar(value="fixed" if has_fixed_cooldown else "tier")

            fixed_radio = ctk.CTkRadioButton(
                cooldown_type_frame,
                text="Fixed Cooldown",
                variable=cooldown_type_var,
                value="fixed"
            )
            fixed_radio.pack(side='left', padx=20)

            tier_radio = ctk.CTkRadioButton(
                cooldown_type_frame,
                text="Tier-Based Cooldown",
                variable=cooldown_type_var,
                value="tier"
            )
            tier_radio.pack(side='left', padx=20)

            # Fixed cooldown
            fixed_cooldown_frame = ctk.CTkFrame(cooldown_frame)
            fixed_cooldown_frame.pack(fill='x', padx=10, pady=5)

            fixed_cooldown_label = ctk.CTkLabel(fixed_cooldown_frame, text="Fixed Cooldown (seconds):")
            fixed_cooldown_label.pack(side='left', padx=10)

            fixed_cooldown_var = tk.IntVar(value=item_config.get('cooldown', 60))
            fixed_cooldown_entry = ctk.CTkEntry(fixed_cooldown_frame, textvariable=fixed_cooldown_var, width=100)
            fixed_cooldown_entry.pack(side='left', padx=10)

            # Tier-based cooldown
            tier_cooldown_frame = ctk.CTkFrame(cooldown_frame)
            tier_cooldown_frame.pack(fill='x', padx=10, pady=5)

            # Number of tiers
            num_tiers_label = ctk.CTkLabel(tier_cooldown_frame, text="Number of Tiers:")
            num_tiers_label.pack(side='left', padx=10)

            # Determine number of tiers
            num_tiers = 5
            if 'cooldowns' in item_config:
                num_tiers = len(item_config['cooldowns'])

            num_tiers_var = tk.IntVar(value=num_tiers)
            num_tiers_values = ["1", "2", "3", "4", "5"]
            num_tiers_dropdown = ctk.CTkOptionMenu(
                tier_cooldown_frame,
                values=num_tiers_values,
                command=lambda value: num_tiers_var.set(int(value))
            )
            num_tiers_dropdown.set(str(num_tiers))
            num_tiers_dropdown.pack(side='left', padx=10)

            # Tier cooldowns
            tier_values_frame = ctk.CTkFrame(cooldown_frame)
            tier_values_frame.pack(fill='x', padx=10, pady=5)

            # Create entries for each tier
            tier_vars = {}
            tier_entries = {}

            for i, tier in enumerate(["I", "II", "III", "IV", "V"]):
                tier_frame = ctk.CTkFrame(tier_values_frame)
                tier_frame.pack(side='left', padx=10)

                tier_label = ctk.CTkLabel(tier_frame, text=f"Tier {tier}:")
                tier_label.pack(pady=2)

                # Get the cooldown for this tier if available
                tier_cooldown = 60 - i*5  # Default values
                if 'cooldowns' in item_config and tier in item_config['cooldowns']:
                    tier_cooldown = item_config['cooldowns'][tier]

                tier_vars[tier] = tk.IntVar(value=tier_cooldown)
                tier_entries[tier] = ctk.CTkEntry(tier_frame, textvariable=tier_vars[tier], width=50)
                tier_entries[tier].pack(pady=2)

            # Options section
            options_frame = ctk.CTkFrame(edit_frame)
            options_frame.pack(fill='x', padx=10, pady=10)

            options_label = ctk.CTkLabel(
                options_frame,
                text="Options",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            options_label.pack(anchor="w", padx=10, pady=(10, 5))

            # Extract tier option
            extract_tier_var = tk.BooleanVar(value=item_config.get('extract_tier', False))
            extract_tier_switch = ctk.CTkSwitch(
                options_frame,
                text="Extract Tier from Pattern",
                variable=extract_tier_var
            )
            extract_tier_switch.pack(anchor='w', padx=20, pady=5)

            # Extract duration option
            extract_duration_var = tk.BooleanVar(value=item_config.get('extract_duration', False))
            extract_duration_switch = ctk.CTkSwitch(
                options_frame,
                text="Extract Duration from Pattern",
                variable=extract_duration_var
            )
            extract_duration_switch.pack(anchor='w', padx=20, pady=5)

            # Note about tier extraction
            tier_note = ctk.CTkLabel(
                options_frame,
                text="Note: Extract Tier is required for tier-based cooldowns",
                text_color="blue"
            )
            tier_note.pack(anchor='w', padx=20, pady=5)

            # Color picker
            color_frame = ctk.CTkFrame(edit_frame)
            color_frame.pack(fill='x', padx=10, pady=10)

            color_label = ctk.CTkLabel(
                color_frame,
                text="Text Color",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            color_label.pack(anchor="w", padx=10, pady=(10, 5))

            color_picker_frame = ctk.CTkFrame(color_frame)
            color_picker_frame.pack(fill='x', padx=10, pady=5)

            color_var = tk.IntVar(value=item_config.get('color', 0x00FF00))

            # Create a frame to show the current color
            color_preview = tk.Frame(color_picker_frame, width=30, height=20, bg=f"#{color_var.get():06x}")
            color_preview.pack(side='left', padx=10)

            def pick_color():
                color_code = colorchooser.askcolor(initialcolor=f"#{color_var.get():06x}")
                if color_code[1]:  # If a color was selected (not cancelled)
                    # Convert from #RRGGBB to 0xRRGGBB
                    hex_color = int(color_code[1][1:], 16)
                    color_var.set(hex_color)
                    color_preview.config(bg=color_code[1])

            color_button = ctk.CTkButton(color_picker_frame, text="Choose Color", command=pick_color)
            color_button.pack(side='left', padx=10)

            # Function to update UI based on cooldown type
            def update_cooldown_ui():
                cooldown_type = cooldown_type_var.get()
                if cooldown_type == "fixed":
                    # Show fixed cooldown, hide tier settings
                    fixed_cooldown_frame.pack(fill='x', padx=10, pady=5)
                    tier_cooldown_frame.pack_forget()
                    tier_values_frame.pack_forget()
                else:
                    # Hide fixed cooldown, show tier settings
                    fixed_cooldown_frame.pack_forget()
                    tier_cooldown_frame.pack(fill='x', padx=10, pady=5)
                    tier_values_frame.pack(fill='x', padx=10, pady=5)

            # Bind the radio buttons to update the UI
            fixed_radio.configure(command=update_cooldown_ui)
            tier_radio.configure(command=update_cooldown_ui)

            # Function to update UI based on extract tier option
            def update_extract_tier_ui():
                if extract_tier_var.get():
                    # If extracting tier, enable tier-based cooldown
                    tier_radio.configure(state="normal")
                else:
                    # If not extracting tier, force fixed cooldown and disable tier-based
                    fixed_radio.select()
                    tier_radio.configure(state="disabled")
                    update_cooldown_ui()

            # Bind the extract tier switch to update the UI
            extract_tier_switch.configure(command=update_extract_tier_ui)

            # Function to save changes
            def save_changes():
                # Get values from form
                display_name = display_name_var.get().strip()
                pattern = pattern_var.get().strip()
                message_template = message_template_var.get().strip()
                color = color_var.get()
                extract_tier = extract_tier_var.get()
                extract_duration = extract_duration_var.get()

                # Validate inputs
                if not display_name or not pattern or not message_template:
                    messagebox.showerror("Error", "Display Name, Pattern, and Message Template are required")
                    return

                # Validate that Extract Tier is enabled when using tier-based cooldowns
                if cooldown_type_var.get() == "tier" and not extract_tier_var.get():
                    messagebox.showerror("Error", "Extract Tier from Pattern must be enabled when using tier-based cooldowns")
                    return

                # Update the item config
                updated_config = {
                    "display_name": display_name,
                    "config_key": item_config.get("config_key", f"show_{item_id}_message"),
                    "pattern": pattern,
                    "message_template": message_template,
                    "color": color,
                    "extract_tier": extract_tier,
                    "extract_duration": extract_duration
                }

                # Add cooldown information
                if cooldown_type_var.get() == "fixed":
                    updated_config["cooldown"] = fixed_cooldown_var.get()
                    # Remove tier-based cooldowns if they exist
                    if "cooldowns" in item_config:
                        del item_config["cooldowns"]
                else:
                    cooldowns = {}
                    num_tiers = num_tiers_var.get()
                    all_tiers = ["I", "II", "III", "IV", "V"]

                    # Only include the selected number of tiers
                    for i, tier in enumerate(all_tiers):
                        if i < num_tiers:
                            cooldowns[tier] = tier_vars[tier].get()

                    updated_config["cooldowns"] = cooldowns
                    # Remove fixed cooldown if it exists
                    if "cooldown" in updated_config:
                        del updated_config["cooldown"]

                # Update ITEM_CONFIGS
                ITEM_CONFIGS[item_id] = updated_config

                # Save the item configurations
                if save_callback:
                    save_callback()

                # Update the item in the listbox
                selection = items_listbox.curselection()
                if selection:
                    items_listbox.delete(selection[0])
                    items_listbox.insert(selection[0], f"{display_name} ({item_id})")
                    items_listbox.selection_set(selection[0])

                # Show success message
                messagebox.showinfo("Success", f"Item '{display_name}' updated successfully!")

                # Close the edit window
                edit_window.destroy()

                # Refresh the details
                show_item_details()

            # Buttons frame
            buttons_frame = ctk.CTkFrame(edit_window)
            buttons_frame.pack(fill='x', padx=20, pady=10)

            # Add the save and cancel buttons
            save_button = ctk.CTkButton(
                buttons_frame,
                text="Save Changes",
                command=save_changes
            )
            save_button.pack(side='left', padx=10, pady=10)

            cancel_button = ctk.CTkButton(
                buttons_frame,
                text="Cancel",
                command=edit_window.destroy
            )
            cancel_button.pack(side='right', padx=10, pady=10)

            # Initialize the UI
            update_cooldown_ui()
            update_extract_tier_ui()

        # Add the buttons
        edit_button = ctk.CTkButton(
            buttons_frame,
            text="Edit Item",
            command=edit_item
        )
        edit_button.pack(side='left', padx=10, pady=10)

        delete_button = ctk.CTkButton(
            buttons_frame,
            text="Delete Item",
            command=delete_item
        )
        delete_button.pack(side='left', padx=10, pady=10)

        close_button = ctk.CTkButton(
            buttons_frame,
            text="Close",
            command=items_window.destroy
        )
        close_button.pack(side='right', padx=10, pady=10)

    # Add the manage items button
    manage_button = ctk.CTkButton(
        manage_items_frame,
        text="View and Manage Existing Items",
        command=show_items_manager
    )
    manage_button.pack(padx=10, pady=10)

    # Initialize the UI
    update_cooldown_ui()
    update_extract_tier_ui()

    return item_creator_tab
