import time
import random
from minescript import (
    player_press_forward,
    player_press_attack,
    player_press_left,
    player_press_right,
    player_press_jump,
    player_set_orientation,
    player_position,
    player_get_targeted_block,
    player_orientation,
    player_inventory,
    getblock,
    execute
)

OBSTACLES = ["minecraft:bedrock", "minecraft:ladder", "minecraft:shroomlight"]
obstacle_detected = False
# Track pitch direction for consistent looping between 26 and 21
pitch_direction = -1  # -1 for down (to 21), 1 for up (to 26)

import time

last_turn_time = 0  # Global variable to track the last 180 turn

def check_for_obstacles_and_turn():
    """
    Continuously check if the block the player is looking at is an obstacle.
    If an obstacle is detected, perform a 180-degree turn and pause mouse movement.
    """
    global obstacle_detected

    targeted_block = player_get_targeted_block()  # Get the block the player is looking at
    if targeted_block:
        # Extract block type
        block_type = targeted_block.type.split("[")[0]  # Remove state data (e.g., [facing=north,...])

        # Check for bedrock at the bottom of the mine
        if block_type == "minecraft:bedrock" and targeted_block.side == "up":
            handle_mine_reset()  # Handle mine reset process
            return

        # Check for other obstacles
        if block_type in OBSTACLES:
            if not obstacle_detected:
                print(f"Obstacle detected: {block_type}. Performing 180-degree turn.")
                perform_180_turn()  # Perform a 180-degree turn
                obstacle_detected = True  # Pause cursor movement
            return
    obstacle_detected = False  # Resume cursor movement when no obstacle is detected

def perform_180_turn():
    """
    Smoothly turn the player 180 degrees while disabling the cursor movement script.
    """
    global current_sweep_index

    # Disable cursor movement during the turn
    current_sweep_index = 0  # Reset to start the sweep from top-left after the turn

    current_yaw, current_pitch = player_orientation()  # Get current orientation
    target_yaw = current_yaw + 180  # Turn 180 degrees

    # Smoothly interpolate yaw (pitch remains the same)
    steps = 15
    delay = 0.02
    for step in range(steps + 1):
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        player_set_orientation(interpolated_yaw, current_pitch)  # Keep pitch unchanged during the turn
        time.sleep(delay)

def handle_mine_reset():
    """
    Handles resetting the mine when bedrock at the bottom is detected.
    Stops all movements, executes `/mine s`, waits 5 seconds, and resumes mining with the specified sequence.
    """
    stop_all_movements()  # Stop all player actions
    execute("/mine t")  # Execute the mine reset command
    time.sleep(5)  # Wait for 5 seconds to let the server load
    follow_reset_sequence()  # Perform the custom sequence


def stop_all_movements():
    """Stop all movement, including forward, attack, and mouse movements."""
    player_press_forward(False)
    player_press_attack(False)
    player_press_left(False)
    player_press_right(False)
    player_press_jump(False)


def follow_reset_sequence():
    """
    Perform the sequence after resetting the mine:
    1. Hold forward until underneath a shroomlight.
    2. Jump.
    3. Hold forward for 3 seconds.
    """
    player_press_forward(True)
    while True:
        x, y, z = player_position()  # Get player's current position
        block_below = getblock(x, y - 1, z)  # Check the block directly below

        if block_below.strip() == "minecraft:shroomlight":  # Strip any whitespace and compare
            player_press_jump(True)
            time.sleep(0.5)  # Small jump delay
            player_press_jump(False)
            break

    player_press_forward(True)
    time.sleep(3)  # Hold forward for 3 seconds
    player_press_forward(False)


def smooth_look_away():
    """Smoothly turn the player around 180 degrees while continuing to hold attack."""
    player_press_attack(True)  # Ensure the attack key remains pressed
    current_yaw, _ = player_orientation()  # Get current orientation
    target_yaw = current_yaw + 180  # Turn around

    # Smoothly interpolate the yaw to look away
    steps = 18  # Adjusted steps for balanced turning speed
    delay = 0.02  # Slightly slower for smoothness
    for step in range(steps + 1):
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        player_set_orientation(interpolated_yaw, 26)  # Fix pitch at 26 degrees
        time.sleep(delay)


def hold_forward_and_mine():
    """
    Perform a sweeping action to clear layers while mining.
    The cursor alternates through the strict sequence:
    yaw +35 → pitch +20 → yaw -35 → pitch -20.
    """
    global current_sweep_index
    current_sweep_index = 0  # Start at the beginning of the sequence

    while True:
        # Phase 1: Hold forward for 0.3 seconds
        player_press_forward(True)
        player_press_attack(True)  # Keep breaking blocks
        start_time = time.time()
        while time.time() - start_time < 0.3:  # Hold forward for 0.3 seconds
            sweep_cursor()  # Perform the sweeping cursor movement
            check_for_obstacles_and_turn()  # Check for obstacles

        # Phase 2: Forward and right sweep for 0.7 seconds
        player_press_forward(True)
        player_press_right(True)
        start_time = time.time()
        while time.time() - start_time < 0.7:  # Hold forward and right for 0.7 seconds
            sweep_cursor()  # Perform the sweeping cursor movement
            check_for_obstacles_and_turn()  # Check for obstacles
        player_press_right(False)

        # Phase 3: Stop forward movement
        player_press_forward(False)

        # Phase 4: Left sweep for 0.7 seconds
        player_press_left(True)
        start_time = time.time()
        while time.time() - start_time < 0.7:  # Hold left for 0.7 seconds
            sweep_cursor()  # Perform the sweeping cursor movement
            check_for_obstacles_and_turn()  # Check for obstacles
        player_press_left(False)

def sweep_cursor_right(pitch, yaw_delta):
    """Smoothly sweep the cursor to the right with smooth yaw and pitch adjustments."""
    current_yaw, current_pitch = player_orientation()  # Get current orientation
    target_yaw = current_yaw + yaw_delta  # Adjust yaw to sweep right
    target_pitch = pitch  # Target pitch for the right sweep

    steps = 15  # Number of steps for smooth movement
    delay = 0.02  # Delay between steps

    for step in range(steps + 1):
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        interpolated_pitch = current_pitch + (step / steps) * (target_pitch - current_pitch)
        player_set_orientation(interpolated_yaw, interpolated_pitch)  # Apply yaw and pitch changes
        time.sleep(delay)


def sweep_cursor_left(pitch, yaw_delta):
    """Smoothly sweep the cursor to the left with smooth yaw and pitch adjustments."""
    current_yaw, current_pitch = player_orientation()  # Get current orientation
    target_yaw = current_yaw + yaw_delta  # Adjust yaw to sweep left
    target_pitch = pitch  # Target pitch for the left sweep

    steps = 15  # Number of steps for smooth movement
    delay = 0.02  # Delay between steps

    for step in range(steps + 1):
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        interpolated_pitch = current_pitch + (step / steps) * (target_pitch - current_pitch)
        player_set_orientation(interpolated_yaw, interpolated_pitch)  # Apply yaw and pitch changes
        time.sleep(delay)

def sweep_cursor():
    """
    Perform cursor movement with the sequence:
    yaw +35 → pitch +20 → yaw -35 → pitch -20.
    """
    global current_sweep_index, obstacle_detected

    if obstacle_detected:
        return  # Skip cursor movement if an obstacle is detected

    # Define the strict sequence of movements
    sequence = [
        {"yaw_offset": 35, "pitch_offset": 0},   # Yaw +35
        {"yaw_offset": 0, "pitch_offset": 20},  # Pitch +20
        {"yaw_offset": -35, "pitch_offset": 0}, # Yaw -35
        {"yaw_offset": 0, "pitch_offset": -20}, # Pitch -20
    ]

    # Get the player's current orientation
    base_yaw, base_pitch = player_orientation()
    yaw_offset = sequence[current_sweep_index]["yaw_offset"]
    pitch_offset = sequence[current_sweep_index]["pitch_offset"]

    # Dynamically calculate target yaw and pitch
    target_yaw = base_yaw + yaw_offset
    target_pitch = base_pitch + pitch_offset

    # Smoothly interpolate yaw and pitch
    steps = 15  # Number of steps for smooth movement
    delay = 0.01  # Delay between steps

    for step in range(steps + 1):
        yaw_ratio = step / steps
        pitch_ratio = step / steps

        interpolated_yaw = base_yaw + yaw_ratio * (target_yaw - base_yaw)
        interpolated_pitch = base_pitch + pitch_ratio * (target_pitch - base_pitch)
        player_set_orientation(interpolated_yaw, interpolated_pitch)
        time.sleep(delay)

    # Move to the next step in the sequence
    current_sweep_index = (current_sweep_index + 1) % len(sequence)

def stop_forward_movement():
    """Stop forward movement without releasing attack."""
    player_press_forward(False)


def smooth_cursor_movement():
    """Continuously move the cursor left and right smoothly with looping pitch adjustments."""
    global pitch_direction

    current_yaw, current_pitch = player_orientation()  # Get current yaw and pitch
    target_yaw = current_yaw + random.uniform(-67.5, 67.5)  # Restrict random yaw to -67.5 to 67.5 degrees
    target_pitch = current_pitch + (pitch_direction * 5)  # Move pitch up or down by 5

    # Reverse pitch direction when reaching 26 or 21
    if target_pitch >= 26:
        pitch_direction = -1
        target_pitch = 26
    elif target_pitch <= 21:
        pitch_direction = 1
        target_pitch = 21

    steps = 15  # Fewer steps for quicker cursor movement
    delay = 0.01  # Smaller delay for faster responsiveness

    for step in range(steps + 1):  # Include the final target in steps
        # Calculate the interpolated yaw and pitch
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        interpolated_pitch = current_pitch + (step / steps) * (target_pitch - current_pitch)
        player_set_orientation(interpolated_yaw, interpolated_pitch)  # Apply smooth yaw and pitch
        time.sleep(delay)


def check_inventory_and_sell():
    """
    Check if the inventory is full by verifying if slot 35 contains an item.
    If the inventory is full, execute the command "/sellall" in smaller steps to avoid blocking checks.
    """
    inventory = player_inventory()  # Get the player's inventory
    for item in inventory:
        if item.slot == 35:  # Check if slot 35 contains an item
            execute_sellall_nonblocking()
            break


def execute_sellall_nonblocking():
    """Execute /sellall command in a non-blocking way."""
    execute("/sellall")  # Execute the sell command
    for _ in range(20):  # Break the cooldown into smaller steps
        time.sleep(0.1)  # Short delay to avoid spamming
        check_for_obstacles_and_turn()  # Continue checking obstacles while waiting


def auto_mining_loop():
    """Main loop for auto-mining."""
    try:
        while True:
            hold_forward_and_mine()  # Start the modified forward-and-stop mining
            check_for_obstacles_and_turn()  # Check for bedrock/ladder/shroomlight and handle turning
            check_inventory_and_sell()  # Check inventory and sell if full
            time.sleep(0.05)  # Very short delay for rapid checking
    finally:
        # Ensure keys are released when the script stops
        stop_all_movements()


if __name__ == "__main__":
    auto_mining_loop()
