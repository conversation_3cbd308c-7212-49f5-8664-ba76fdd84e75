import minescript
import time

def hold_and_attack():
    # Start holding forward and attack
    minescript.player_press_forward(True)
    minescript.player_press_attack(True)

    try:
        last_position = None  # Track the last matched position to avoid duplicate actions

        while True:
            # Get the player's current position and round coordinates
            position = minescript.player_position()
            x, y, z = map(lambda v: round(v, 1), position)  # Round to one decimal for precision

            # Check each coordinate range
            if position_in_range(x, z, -166, -34, -5.5, -4.5, last_position):
                toggle_forward_and_attack(False)  # Stop moving and attacking
                smooth_turn_shortest_path(135, 26)
                toggle_forward_and_attack(True)  # Resume moving and attacking
                execute_sell_command()
                last_position = (-166, -34, -5)  # Lock position
            elif position_in_range(x, z, -171.5, -170.5, -113, -10, last_position):
                toggle_forward_and_attack(False)  # Stop moving and attacking
                smooth_turn_shortest_path(45, 26)
                toggle_forward_and_attack(True)  # Resume moving and attacking
                execute_sell_command()
                last_position = (-171, None, -10)  # Lock position
            elif position_in_range(x, z, -288, -176, -5.5, -4.5, last_position):
                toggle_forward_and_attack(False)  # Stop moving and attacking
                smooth_turn_shortest_path(-45, 26)
                toggle_forward_and_attack(True)  # Resume moving and attacking
                execute_sell_command()
                last_position = (-288, -176, -5)  # Lock position
            elif position_in_range(x, z, -171.5, -170.5, 0, 132, last_position):
                toggle_forward_and_attack(False)  # Stop moving and attacking
                smooth_turn_shortest_path(-135, 26)
                toggle_forward_and_attack(True)  # Resume moving and attacking
                execute_sell_command()
                last_position = (-171, None, 0)  # Lock position

            # Pause briefly to ensure smooth operation
            time.sleep(0.005)  # Check frequently (5ms)
    except KeyboardInterrupt:
        # Stop holding forward and attack when interrupted
        toggle_forward_and_attack(False)
        minescript.echo("Stopped holding W and attack.")

def position_in_range(x, z, x_min, x_max, z_min, z_max, last_position):
    """
    Check if the player's position is in the target range and prevent re-triggering
    for the same position.
    """
    if last_position and x_min <= last_position[0] <= x_max and z_min <= last_position[2] <= z_max:
        return False  # Skip if the position is already locked
    return x_min <= x <= x_max and z_min <= z <= z_max

def toggle_forward_and_attack(state):
    """
    Enable or disable forward and attack actions based on the state.
    """
    minescript.player_press_forward(state)
    minescript.player_press_attack(state)

def smooth_turn_shortest_path(target_yaw, target_pitch):
    """
    Smoothly turn the player towards the target orientation using the shortest path.
    """
    current_yaw, current_pitch = minescript.player_orientation()
    yaw_diff = ((target_yaw - current_yaw + 180) % 360) - 180  # Normalize yaw difference
    yaw_step = yaw_diff / 20  # Divide into 20 steps
    pitch_step = (target_pitch - current_pitch) / 20

    for _ in range(20):
        current_yaw += yaw_step
        current_pitch += pitch_step
        minescript.player_set_orientation(current_yaw, current_pitch)
        time.sleep(0.005)  # 5ms delay for each step to simulate smooth movement

def execute_sell_command():
    """
    Execute the "/sell" command.
    """
    minescript.execute("/sell")
    minescript.echo("Executed /sell command.")


if __name__ == "__main__":
    hold_and_attack()
