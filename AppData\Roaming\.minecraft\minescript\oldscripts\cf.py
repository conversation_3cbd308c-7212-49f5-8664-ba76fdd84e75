import minescript
import time
import re
import requests  # Import requests for sending POST to Discord

# Define your Discord webhook URL here
DISCORD_WEBHOOK_URL = "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL_HERE"

def send_to_discord(message):
    """Send a message to <PERSON>rd via webhook."""
    data = {
        "content": message  # The content of the message to send to Discord
    }
    response = requests.post(DISCORD_WEBHOOK_URL, json=data)
    if response.status_code != 204:
        minescript.echo(f"Failed to send message to Discord. Status Code: {response.status_code}")

def extract_text_value(item_str):
    """Extract and clean the part of the item details that contains the dynamic value of 'text', combining single letters into words and ensuring 'Auction Details', 'Price', and 'Poster' are at the end with their values."""
    # Regular expression to find the "text":"<value>" pattern and capture the dynamic text, including empty fields
    pattern = r'"text":"([^"]*)"'  # This captures the value inside the "text":"<value>", including empty strings
    matches = re.findall(pattern, item_str)

    # Initialize a list to store the combined words and special fields for reordering
    words = []
    auction_details = ''
    price = ''
    poster = ''
    current_word = []
    capture_next_text = None  # Track if we need to attach the next text to a specific field (e.g., Price or Poster)

    for match in matches:
        # Remove "?" symbols without affecting the rest of the line
        match = match.replace("?", "")  # Remove only "?" from the current match

        if match:  # If the match has content (not empty), it's part of a word
            if "Auction Details" in match:
                auction_details = match  # Store Auction Details
            elif "Price" in match:
                price = match  # Store Price label
                capture_next_text = 'Price'  # Flag to capture the next text as part of the price
            elif "Poster" in match:
                poster = match  # Store Poster label
                capture_next_text = 'Poster'  # Flag to capture the next text as part of the poster
            elif capture_next_text == 'Price':
                price += f" {match}"  # Attach the next text to the Price label
                capture_next_text = None  # Reset after capturing
            elif capture_next_text == 'Poster':
                poster += f" {match}"  # Attach the next text to the Poster label
                capture_next_text = None  # Reset after capturing
            else:
                current_word.append(match)
        else:  # If the match is empty, we treat it as a word separator
            if current_word:  # If we have collected letters for a word, join them and add to words list
                words.append(''.join(current_word))
                current_word = []  # Reset for the next word

    # Add the final word if there are any remaining letters
    if current_word:
        words.append(''.join(current_word))

    # Exclude specific unwanted phrases and time-related information
    excluded_phrases = [
        "?", "(", "Expires In", "Category", "Miscellaneous", "Left Click", "to", "Purchase", "Right Click",
        "Building Blocks", "Decorations", "Redstone", "Transport", "Food", "Tools", 
        "Combat", "Brewing", "Runes", "Crate Keys", "Enchanted Books", "Tag Vouchers", 
        "Mob Spawners", "Contraband", "Furniture"
    ]
    
    # Filter out any words that match excluded phrases or time-related terms (days/hours)
    filtered_words = [word for word in words if 'days' not in word and 'hours' not in word and all(phrase not in word for phrase in excluded_phrases)]

    # If there are more than 4 words, take the 4th-to-last and move it to the top
    if len(filtered_words) >= 4:
        fourth_to_last = filtered_words[-4]
        filtered_words = [fourth_to_last] + filtered_words[:len(filtered_words) - 4] + filtered_words[len(filtered_words) - 3:]

    # Append the reserved fields (Auction Details, Price, Poster) at the end in the specified order
    if auction_details:
        filtered_words.append(auction_details)
    if price:
        filtered_words.append(price)
    if poster:
        filtered_words.append(poster)

    # Return filtered, combined words with reserved fields at the end, or a message if none found
    return filtered_words if filtered_words else ["No valid 'text' field found"]

def main():
    # Initialize a variable to track the previous item in slot 0
    previous_item = None
    last_ah_check = time.time()

    # Continuously check for updates in slot 0
    while True:
        # Every 60 seconds, ensure the Auction House is open
        current_time = time.time()
        if current_time - last_ah_check >= 60:
            container_items = minescript.container_get_items()
            
            # If the container is not open or items are not available, open the Auction House
            if container_items is None or not any(item.slot == 0 for item in container_items):
                send_to_discord("Auction House is not open. Executing /ah")
                minescript.execute("/ah")
            last_ah_check = current_time

        # Retrieve all items in the container
        container_items = minescript.container_get_items()

        # Check if container_items is None
        if container_items is None:
            send_to_discord("No items found in container.")
            time.sleep(2)
            continue

        # Check if there is an item in slot 0
        for item in container_items:
            if item.slot == 0:  # Check if the item is in slot 0
                # Convert the item to a string and extract the 'text' values
                item_str = str(item)
                text_values = extract_text_value(item_str)
                
                # Check if the current item is different from the previous one
                if previous_item != text_values:
                    # Echo the new item details to Discord
                    for text_value in text_values:
                        send_to_discord(text_value)  # Send combined words to Discord webhook

                    # Update the previous item to the current one
                    previous_item = text_values
                break

        # Wait before checking again (adjust the sleep duration as needed)
        time.sleep(2)

if __name__ == "__main__":
    main()
