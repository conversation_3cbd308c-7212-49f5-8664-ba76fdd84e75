
[21:47:31] [Render thread/INFO]: Processing command from chat event: \miner
[21:47:32] [Render thread/INFO]: [CHAT] Traceback (most recent call last):
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Roaming\.minecraft\minescript\miner.py", line 34, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     import flet as ft
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flet\__init__.py", line 1, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from flet.app import app, app_async
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flet\app.py", line 12, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from flet.core.page import Page
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flet\core\page.py", line 108, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from flet.auth.authorization import Authorization
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flet\auth\__init__.py", line 1, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from flet.auth.authorization import Authorization
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flet\auth\authorization.py", line 8, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     import httpx
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\__init__.py", line 2, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from ._api import *
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_api.py", line 6, in <module>
[21:47:32] [job-1-miner/INFO]: Script process exited with 1 for job `[1] Running: miner`
[21:47:32] [job-1-miner/INFO]: Job `[1] miner` exited with code 1, draining message queues...
[21:47:32] [Render thread/INFO]: [CHAT]     from ._client import Client
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py", line 30, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from ._transports.asgi import ASGITransport
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_transports\__init__.py", line 3, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from .default import *
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_transports\default.py", line 33, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     import httpcore
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpcore\__init__.py", line 1, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from ._api import request, stream
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpcore\_api.py", line 5, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from ._sync.connection_pool import ConnectionPool
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpcore\_sync\__init__.py", line 1, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from .connection import HTTPConnection
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpcore\_sync\connection.py", line 12, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from .._synchronization import Lock
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpcore\_synchronization.py", line 11, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     import trio
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\trio\__init__.py", line 23, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from ._core import TASK_STATUS_IGNORED as TASK_STATUS_IGNORED  # isort: split
[21:47:32] [Render thread/INFO]: [CHAT]     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\trio\_core\__init__.py", line 21, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from ._local import RunVar, RunVarToken
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\trio\_core\_local.py", line 9, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from . import _run
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\trio\_core\_run.py", line 44, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     from ._thread_cache import start_thread_soon
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\trio\_core\_thread_cache.py", line 91, in <module>
[21:47:32] [Render thread/INFO]: [CHAT]     set_os_thread_name = get_os_thread_name_func()
[21:47:32] [Render thread/INFO]: [CHAT]                          ^^^^^^^^^^^^^^^^^^^^^^^^^
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\trio\_core\_thread_cache.py", line 52, in get_os_thread_name_func
[21:47:32] [Render thread/INFO]: [CHAT]     libpthread_path = ctypes.util.find_library("pthread")
[21:47:32] [Render thread/INFO]: [CHAT]                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[21:47:32] [Render thread/INFO]: [CHAT]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\ctypes\util.py", line 59, in find_library
[21:47:32] [Render thread/INFO]: [CHAT]     for directory in os.environ['PATH'].split(os.pathsep):
[21:47:32] [Render thread/INFO]: [CHAT]                      ~~~~~~~~~~^^^^^^^^
[21:47:32] [Render thread/INFO]: [CHAT]   File "<frozen os>", line 714, in __getitem__
[21:47:32] [Render thread/INFO]: [CHAT] KeyError: 'PATH'
