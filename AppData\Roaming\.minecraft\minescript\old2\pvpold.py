import time
import re
from draw_text import draw_centered_string
from minescript import EventQueue, EventType
from lib_java import JavaClass

# Global variables to store chat messages and their text objects
chat_messages = []
chat_text_objects = []
max_messages = 5  # Maximum number of messages to display at once
new_message_y = 80  # Fixed position for the newest message

# Regular expressions to extract information from the messages
pattern_1 = re.compile(r"RUNES.*You have disabled the rune effects of (.*) with your Runic Obstruction .* for (.*) seconds.")
pattern_2 = re.compile(r"RUNES.*(.*) has prevented your custom enchants from working for (.*) seconds with their Runic Obstruction .*.")
pattern_3 = re.compile(r"RUNES.* (.*) has a combo of 3 on you! Your Ra's Wrath IV has activated!")  # Updated pattern for Ra's Wrath

# Store the last time the countdown was updated
last_update_time = time.time()

def filter_and_format_message(message):
    """Filter the message, extract details, and format the output."""
    match_1 = pattern_1.search(message)
    match_2 = pattern_2.search(message)
    match_3 = pattern_3.search(message)
    
    if match_1:
        name, time_duration = match_1.groups()
        time_duration = int(time_duration)  # Convert time to integer
        # Format the message: (name) is RUNICED for (time) seconds with "RUNICED" in green
        return (f"{name} is RUNICED for {time_duration} seconds", 0x00ff00, time_duration)

    elif match_2:
        name, time_duration = match_2.groups()
        time_duration = int(time_duration)  # Convert time to integer
        # Format the message: You are RUNICED for (time) seconds with "RUNICED" in red
        return (f"You are RUNICED for {time_duration} seconds", 0xff0000, time_duration)
    
    elif match_3:
        name = match_3.group(1)
        # Format the new message: "Anc helm procced on (name) for 7 seconds"
        return (f"Anc helm procced on {name} for 7 seconds", 0x00ff00, 7)  # Green color

    return None

def remove_duplicate_message(message):
    """Remove an old duplicate message from chat_messages and chat_text_objects."""
    global chat_messages, chat_text_objects

    # Extract the base part of the message (before "for x seconds") to compare
    base_message = message.split('for')[0]

    # Check if the message with the same base content already exists
    for i, (msg, color, time_left) in enumerate(chat_messages):
        if base_message in msg:
            # Remove the old message and text object
            chat_messages.pop(i)
            chat_text_objects.pop(i)
            break

def display_message(msg, color, time_left):
    """Display a new message immediately on the screen."""
    global chat_text_objects

    # Calculate the position for the new message
    x = 250
    y = new_message_y - (len(chat_messages) - 1) * 5
    scale = 2

    # Draw a new centered message with countdown
    text_obj = draw_centered_string(msg, x=x, y=y, color=color, scale=scale)
    chat_text_objects.append(text_obj)

def on_chat_message(message):
    """Handle chat messages, filter and format them, and update the rendered text."""
    global chat_messages, chat_text_objects
    
    # Process the message, filter and format it
    formatted_message = filter_and_format_message(message)
    if not formatted_message:
        return

    msg, color, time_left = formatted_message

    # Remove any duplicate message
    remove_duplicate_message(msg)

    # Append the new formatted message to the chat message list
    chat_messages.append(formatted_message)

    # Display the message immediately
    display_message(msg, color, time_left)
    
    # If there are more messages than allowed, remove the oldest one
    if len(chat_messages) > max_messages:
        chat_messages.pop(0)
        chat_text_objects.pop(0)

def update_messages():
    """Update the countdown for each message and remove messages when the timer hits zero."""
    global chat_messages, chat_text_objects, last_update_time

    # Get the current time
    current_time = time.time()
    
    # Calculate how much time has passed since the last update
    time_elapsed = current_time - last_update_time

    if time_elapsed >= 1:  # Only update the countdown once a full second has passed
        # Update each message's countdown
        for i in range(len(chat_messages) - 1, -1, -1):  # Iterate in reverse order to avoid index issues
            msg, color, time_left = chat_messages[i]
            
            # Reduce the time left by 1 second
            time_left -= int(time_elapsed)
            
            if time_left <= 0:
                # Remove the message if time hits zero
                chat_messages.pop(i)
                chat_text_objects.pop(i)
            else:
                # Update the message with the new time
                updated_message = msg.split('for')[0] + f"for {time_left} seconds"
                chat_messages[i] = (updated_message, color, time_left)

                # Recalculate the y-position for each message
                x = 250
                y = new_message_y - (len(chat_messages) - 1 - i) * 5
                scale = 2

                # Update the existing text object
                chat_text_objects[i].string.set_value(updated_message)  # Update the message string
                chat_text_objects[i].y.set_value(y)  # Update the position
                chat_text_objects[i].color.set_value(color)  # Update the color

        # Reset the last update time
        last_update_time = current_time

def main():
    """Continuously monitor chat for updates, update the countdown, and display the filtered messages."""
    with EventQueue() as event_queue:
        # Register chat listener to capture chat messages
        event_queue.register_chat_listener()

        while True:
            try:
                # Non-blocking call to get chat events, timeout after 0.1 seconds for faster response
                event = event_queue.get(timeout=0.1)

                if event and event.type == EventType.CHAT:
                    # Handle the chat message immediately when received
                    on_chat_message(event.message)

            except Exception:
                # If no event is received within the timeout, we pass and continue the loop
                pass

            # Update message countdown based on real time
            update_messages()

if __name__ == "__main__":
    main()
