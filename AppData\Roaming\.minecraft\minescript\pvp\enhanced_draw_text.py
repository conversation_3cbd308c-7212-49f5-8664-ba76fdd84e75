# SPDX-FileCopyrightText: © 2024 Enhanced by Augment AI, original by <PERSON> <<EMAIL>>
# SPDX-License-Identifier: MIT

r"""enhanced_draw_text - Modern text rendering for Minecraft

An enhanced version of draw_text with improved features:
- Better compatibility across Minecraft versions
- Shadow and outline text effects
- Animation capabilities
- Improved error handling

Requires:
  minescript v4.0+
  lib_java v2+

Usage as standalone script:
```
\enhanced_draw_text TEXT [X Y [HEX_COLOR [SCALE [SHADOW [OUTLINE]]]]]
```

Examples as commands:
```
\enhanced_draw_text "some text"
\enhanced_draw_text "Hello, world!" 10 10
\enhanced_draw_text "green text" 360 10 0x00ff00
\enhanced_draw_text "hello yellow with shadow" 190 100 0xffff00 4 true
\enhanced_draw_text "outlined text" 200 100 0xff0000 4 false true
```

Example as imported library:
```
from enhanced_draw_text import (draw_string, draw_centered_string, draw_string_with_effects)
import time
text = draw_string("some white text", x=20, y=20, color=0xffffff, scale=4)
time.sleep(5)
text.x.set_value(25)  # Move text to the right.
time.sleep(1)
text.y.set_value(25)  # Move text down.
time.sleep(1)
text.color.set_value(0xff0000)  # Change text color to red.
text.string.set_value("now it's red")  # Change text string.
time.sleep(2)

# With shadow effect
text_shadow = draw_string_with_effects("text with shadow", x=20, y=50, color=0xffff00, scale=4, shadow=True)
```
"""

from dataclasses import dataclass
import sys
import time
import traceback

from minescript_runtime import JavaException

from minescript import (
  cancel_scheduled_tasks,
  render_loop,
  schedule_render_tasks,
  script_loop,
  version_info,
  echo
)

try:
    from lib_java import (
      Float,
      JavaClass,
      JavaFloat,
      JavaInt,
      JavaObject,
      JavaString,
      TaskRecorder,
      java_class_map,
      java_member_map,
    )
except ImportError:
    echo("§c§lError: §r§7lib_java module is required for enhanced_draw_text")
    raise

from typing import Callable, List, Optional, Dict, Any, Union, Tuple

# Get Minecraft version information
versions = version_info()
mc_version = [int(v) for v in versions.minecraft.split(".")]
mc_version_str = versions.minecraft

# Set up Java class mappings based on Minecraft version
if versions.minecraft_class_name == "net.minecraft.class_310":
    java_class_map.update({
        "net.minecraft.client.Minecraft": "net.minecraft.class_310",
        "net.minecraft.client.gui.GuiGraphics": "net.minecraft.class_332",
        "com.mojang.blaze3d.vertex.PoseStack": "net.minecraft.class_4587",
        "com.mojang.blaze3d.vertex.VertexSorting": "net.minecraft.class_8251",
        "com.mojang.blaze3d.ProjectionType": "net.minecraft.class_10366",
    })
    java_member_map.update({
        "getInstance": "method_1551",
        "ON_OSX": "field_1703",
        "gui": "field_1705",
        "getFont": "method_1756",
        "renderBuffers": "method_22940",
        "bufferSource": "method_23000",
        "getWindow": "method_22683",
        "ORTHOGRAPHIC": "field_54954",
        "ORTHOGRAPHIC_Z": "field_43361",
        "getWidth": "method_4489",
        "getHeight": "method_4506",
        "drawString": "method_25303",
        "drawCenteredString": "method_25300",
        "pushPose": "method_22903",
        "setIdentity": "method_34426",
        "popPose": "method_22909",
        "flush": "method_51452",
    })
    if mc_version <= [1, 20, 4]:
        java_member_map.update({
            "scale": "method_22905",
            "translate": "method_22904",
        })

# Initialize Minecraft class
Minecraft = JavaClass("net.minecraft.client.Minecraft")

try:
    minecraft = Minecraft.getInstance()
except JavaException as e:
    if versions.mod_loader == "Forge" and mc_version <= [1, 20, 4]:
        # Some versions use an unobfuscated Minecraft class name but obfuscated names otherwise.
        java_member_map["getInstance"] = "m_91087_"
        minecraft = Minecraft.getInstance()
        java_member_map.update({
            "ON_OSX": "f_91002_",
            "gui": "f_91065_",
            "getFont": "m_93082_",
            "renderBuffers": "m_91269_",
            "bufferSource": "m_110104_",
            "getWindow": "m_91268_",
            "ORTHOGRAPHIC_Z": "f_276633_",
            "getWidth": "m_85441_",
            "getHeight": "m_85442_",
            "drawString": "m_280488_",
            "drawCenteredString": "m_280137_",
            "pushPose": "m_85836_",
            "setIdentity": "m_166856_",
            "scale": "m_85841_",
            "translate": "m_252880_",
            "popPose": "m_85849_",
        })
    else:
        raise e

DEFAULT_SCALE = 4
DEFAULT_COLOR = 0xFFFFFF
SHADOW_OFFSET = 1
OUTLINE_OFFSET = 1

# Global variable to store reference to current text object
_current_text_obj = None

with script_loop:
    try:
        Numbers = JavaClass("net.minescript.common.Numbers")

        ON_OSX = Minecraft.ON_OSX
        font = minecraft.gui.getFont()

        GuiGraphics = JavaClass("net.minecraft.client.gui.GuiGraphics")
        with render_loop:
            guiGraphics = GuiGraphics(minecraft, minecraft.renderBuffers().bufferSource())
        window = minecraft.getWindow()

        Matrix4f = JavaClass("org.joml.Matrix4f")
        RenderSystem = JavaClass("com.mojang.blaze3d.systems.RenderSystem")
        if mc_version <= [1, 21, 1]:
            VertexSorting = JavaClass("com.mojang.blaze3d.vertex.VertexSorting")
            ORTHOGRAPHIC_Z = VertexSorting.ORTHOGRAPHIC_Z
            matrix4f = Matrix4f().setOrtho(0, window.getWidth(), window.getHeight(), 0, 1000, 3000)
            z_translation = JavaFloat(-2000)
        else:
            ProjectionType = JavaClass("com.mojang.blaze3d.ProjectionType")
            ORTHOGRAPHIC_Z = ProjectionType.ORTHOGRAPHIC
            matrix4f = Matrix4f().setOrtho(0, window.getWidth(), window.getHeight(), 0, 1000, 21000)
            z_translation = JavaFloat(-11000)

        default_scale = JavaFloat(DEFAULT_SCALE)
        zero = JavaFloat(0)
        one = JavaFloat(1)
    except Exception as e:
        echo(f"§c§lError initializing enhanced_draw_text: §r§7{str(e)}")
        traceback.print_exc()
        raise

use_matrix4f_stack = (mc_version >= [1, 20, 6])

@dataclass
class EnhancedText:
    """Enhanced text object with additional properties for effects."""
    render_func_id: int
    string: JavaObject
    x: JavaObject
    y: JavaObject
    color: JavaObject
    scale: JavaObject
    shadow: bool = False
    outline: bool = False
    visible: bool = True

    def __del__(self):
        """Clean up render tasks when object is deleted."""
        try:
            cancel_scheduled_tasks(self.render_func_id)
        except:
            pass

    def set_value(self, new_text):
        """Update the text value."""
        self.string.set_value(new_text)

    def set_position(self, x, y):
        """Update the position of the text."""
        self.x.set_value(x)
        self.y.set_value(y)

    def set_color(self, color):
        """Update the color of the text."""
        self.color.set_value(color)

    def set_scale(self, scale):
        """Update the scale of the text."""
        self.scale.set_value(scale)

    def set_visible(self, visible):
        """Set visibility of the text."""
        if visible != self.visible:
            self.visible = visible
            if visible:
                self.string.set_value(self.string.get_value())
            else:
                # Store current value but display empty string
                self._stored_value = self.string.get_value()
                self.string.set_value("")

    def __str__(self):
        """Return the text string."""
        return self.string.get_value()


def draw_centered_string(text, x, y, color=DEFAULT_COLOR, scale=DEFAULT_SCALE):
    """Draw text centered at the specified position."""
    return _draw_string_impl(guiGraphics.drawCenteredString, text, x, y, color, scale)


def draw_string(text, x, y, color=DEFAULT_COLOR, scale=DEFAULT_SCALE):
    """Draw text at the specified position."""
    return _draw_string_impl(guiGraphics.drawString, text, x, y, color, scale)


def draw_centered_string_with_effects(text, x, y, color=DEFAULT_COLOR, scale=DEFAULT_SCALE, shadow=False, outline=False):
    """Draw centered text with optional shadow and outline effects."""
    return _draw_string_with_effects_impl(guiGraphics.drawCenteredString, text, x, y, color, scale, shadow, outline)


def draw_string_with_effects(text, x, y, color=DEFAULT_COLOR, scale=DEFAULT_SCALE, shadow=False, outline=False):
    """Draw text with optional shadow and outline effects."""
    return _draw_string_with_effects_impl(guiGraphics.drawString, text, x, y, color, scale, shadow, outline)


def _draw_string_impl(draw_method, text, x, y, color, scale):
    """Internal implementation for drawing text."""
    with script_loop:
        try:
            text_var = JavaString(text)
            x_var = JavaInt(x)
            y_var = JavaInt(y)
            color_var = JavaInt(color)
            scale_var = JavaFloat(scale)

            task_recorder = TaskRecorder()
            with task_recorder:
                if mc_version <= [1, 21, 1]:
                    # Adapted from Minecraft::renderFpsMeter
                    RenderSystem.clear(256, ON_OSX)
                    RenderSystem.setProjectionMatrix(matrix4f, ORTHOGRAPHIC_Z)
                    modelViewStack = RenderSystem.getModelViewStack()

                    if use_matrix4f_stack:
                        modelViewStack.pushMatrix()
                    else:
                        modelViewStack.pushPose()
                        modelViewStack.setIdentity()

                    modelViewStack.scale(scale_var, scale_var, one)
                    modelViewStack.translate(zero, zero, z_translation)
                    RenderSystem.applyModelViewMatrix()

                    RenderSystem.lineWidth(one)
                    text_scale = Numbers.divide(scale_var, default_scale)
                    draw_method(
                        font, text_var,
                        Numbers.divide(x_var, text_scale).intValue(), Numbers.divide(y_var, text_scale).intValue(),
                        color_var)

                    if use_matrix4f_stack:
                        modelViewStack.popMatrix()
                    else:
                        modelViewStack.popPose()

                    RenderSystem.applyModelViewMatrix()
                else:  # mc_version > [1, 21, 1]
                    # Adapted from GameRenderer::render
                    RenderSystem.clear(256)
                    RenderSystem.setProjectionMatrix(matrix4f, ORTHOGRAPHIC_Z)
                    matrix4fStack = RenderSystem.getModelViewStack()
                    matrix4fStack.pushMatrix()
                    matrix4fStack.translation(zero, zero, z_translation)
                    matrix4fStack.scale(scale_var, scale_var, one)

                    RenderSystem.lineWidth(one)
                    text_scale = Numbers.divide(scale_var, default_scale)
                    draw_method(
                        font, text_var,
                        Numbers.divide(x_var, text_scale).intValue(), Numbers.divide(y_var, text_scale).intValue(),
                        color_var)
                    guiGraphics.flush()
                    matrix4fStack.popMatrix()

            render_func_id = schedule_render_tasks(task_recorder.recorded_tasks())
            return EnhancedText(render_func_id, text_var, x_var, y_var, color_var, scale_var)
        except Exception as e:
            echo(f"§c§lError drawing text: §r§7{str(e)}")
            traceback.print_exc()
            # Return a dummy text object that won't crash when methods are called on it
            return _create_dummy_text_object(text)


def _draw_string_with_effects_impl(draw_method, text, x, y, color, scale, shadow, outline):
    """Internal implementation for drawing text with effects."""
    with script_loop:
        try:
            text_var = JavaString(text)
            x_var = JavaInt(x)
            y_var = JavaInt(y)
            color_var = JavaInt(color)
            scale_var = JavaFloat(scale)

            task_recorder = TaskRecorder()
            with task_recorder:
                if mc_version <= [1, 21, 1]:
                    # Adapted from Minecraft::renderFpsMeter
                    RenderSystem.clear(256, ON_OSX)
                    RenderSystem.setProjectionMatrix(matrix4f, ORTHOGRAPHIC_Z)
                    modelViewStack = RenderSystem.getModelViewStack()

                    if use_matrix4f_stack:
                        modelViewStack.pushMatrix()
                    else:
                        modelViewStack.pushPose()
                        modelViewStack.setIdentity()

                    modelViewStack.scale(scale_var, scale_var, one)
                    modelViewStack.translate(zero, zero, z_translation)
                    RenderSystem.applyModelViewMatrix()

                    RenderSystem.lineWidth(one)
                    text_scale = Numbers.divide(scale_var, default_scale)
                    scaled_x = Numbers.divide(x_var, text_scale).intValue()
                    scaled_y = Numbers.divide(y_var, text_scale).intValue()

                    # Draw outline if requested
                    if outline:
                        shadow_color = JavaInt(0x000000)  # Black outline
                        # Draw text at 8 positions around the original
                        offsets = [(-1, -1), (0, -1), (1, -1), (-1, 0), (1, 0), (-1, 1), (0, 1), (1, 1)]
                        for offset_x, offset_y in offsets:
                            draw_method(font, text_var, scaled_x + offset_x, scaled_y + offset_y, shadow_color)

                    # Draw shadow if requested
                    if shadow:
                        shadow_color = JavaInt(color // 4)  # Darker version of the text color
                        # Use integer offsets directly instead of trying to add to JavaInt
                        draw_method(font, text_var, scaled_x + 1, scaled_y + 1, shadow_color)

                    # Draw the main text
                    draw_method(font, text_var, scaled_x, scaled_y, color_var)

                    if use_matrix4f_stack:
                        modelViewStack.popMatrix()
                    else:
                        modelViewStack.popPose()

                    RenderSystem.applyModelViewMatrix()
                else:  # mc_version > [1, 21, 1]
                    # Adapted from GameRenderer::render
                    RenderSystem.clear(256)
                    RenderSystem.setProjectionMatrix(matrix4f, ORTHOGRAPHIC_Z)
                    matrix4fStack = RenderSystem.getModelViewStack()
                    matrix4fStack.pushMatrix()
                    matrix4fStack.translation(zero, zero, z_translation)
                    matrix4fStack.scale(scale_var, scale_var, one)

                    RenderSystem.lineWidth(one)
                    text_scale = Numbers.divide(scale_var, default_scale)
                    scaled_x = Numbers.divide(x_var, text_scale).intValue()
                    scaled_y = Numbers.divide(y_var, text_scale).intValue()

                    # Draw outline if requested
                    if outline:
                        shadow_color = JavaInt(0x000000)  # Black outline
                        # Draw text at 8 positions around the original
                        offsets = [(-1, -1), (0, -1), (1, -1), (-1, 0), (1, 0), (-1, 1), (0, 1), (1, 1)]
                        for offset_x, offset_y in offsets:
                            draw_method(font, text_var, scaled_x + offset_x, scaled_y + offset_y, shadow_color)

                    # Draw shadow if requested
                    if shadow:
                        shadow_color = JavaInt(color // 4)  # Darker version of the text color
                        # Use integer offsets directly instead of trying to add to JavaInt
                        draw_method(font, text_var, scaled_x + 1, scaled_y + 1, shadow_color)

                    # Draw the main text
                    draw_method(font, text_var, scaled_x, scaled_y, color_var)

                    guiGraphics.flush()
                    matrix4fStack.popMatrix()

            render_func_id = schedule_render_tasks(task_recorder.recorded_tasks())
            return EnhancedText(render_func_id, text_var, x_var, y_var, color_var, scale_var, shadow, outline)
        except Exception as e:
            echo(f"§c§lError drawing text with effects: §r§7{str(e)}")
            traceback.print_exc()
            # Return a dummy text object that won't crash when methods are called on it
            return _create_dummy_text_object(text)


def _create_dummy_text_object(text):
    """Create a dummy text object for fallback when rendering fails."""
    class DummyJavaObject:
        def __init__(self, value):
            self._value = value

        def set_value(self, value):
            self._value = value

        def get_value(self):
            return self._value

    text_var = DummyJavaObject(text)
    x_var = DummyJavaObject(0)
    y_var = DummyJavaObject(0)
    color_var = DummyJavaObject(DEFAULT_COLOR)
    scale_var = DummyJavaObject(DEFAULT_SCALE)

    return EnhancedText(-1, text_var, x_var, y_var, color_var, scale_var)


def main():
    """Main function when script is run directly."""
    args = sys.argv[1:]
    if len(args) == 0:
        raise ValueError(r"Usage: \enhanced_draw_text TEXT [X Y [HEX_COLOR [SCALE [SHADOW [OUTLINE]]]]]")

    text = args[0]
    x = int(args[1]) if len(args) > 2 else 10
    y = int(args[2]) if len(args) > 2 else 10
    color = int(args[3], 16) if len(args) > 3 else DEFAULT_COLOR
    scale = float(args[4]) if len(args) > 4 else DEFAULT_SCALE
    shadow = args[5].lower() == "true" if len(args) > 5 else False
    outline = args[6].lower() == "true" if len(args) > 6 else False

    # Create the text object - we need to keep a reference to prevent it from being garbage collected
    if shadow or outline:
        text_obj = draw_string_with_effects(text, x, y, color, scale, shadow, outline)
    else:
        text_obj = draw_string(text, x, y, color, scale)

    # Store the reference in a global variable to prevent garbage collection
    global _current_text_obj
    _current_text_obj = text_obj

    echo(f"§a§lText displayed: §r§7{text}")
    echo(f"§a§lMinecraft version: §r§7{mc_version_str}")

    while True:
        time.sleep(60)


if __name__ == "__main__":
    main()
