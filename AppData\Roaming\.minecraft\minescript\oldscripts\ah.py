import time
import customtkinter as ctk
import threading
import random
import math
import os 
import sys
from minescript import (
    player_press_forward,
    player_press_attack,
    player_press_left,
    player_press_right,
    player_press_jump,
    player_set_orientation,
    player_position,
    player_get_targeted_block,
    player_orientation,
    player_inventory,
    getblock,
    chat,
    echo,
    execute
)

config = {
    "mouse_speed": 0.005,
    "default_steps": 10,
    "script_running": False,
    "restart_in_progress": False,  # New flag to indicate a restart
}

CONFIG_FILE = "prisonconfig.txt"
sys.stderr.flush()
terminate_script = False
stop_called = False
script_lock = threading.Lock()


def read_config():
    """
    Reads the configuration from the prisonconfig.txt file.
    """
    if os.path.exists(CONFIG_FILE):
        echo(f"Config file found: {CONFIG_FILE}. Reading configurations...")
        try:
            with open(CONFIG_FILE, "r") as f:
                lines = f.readlines()
                for line in lines:
                    key, value = line.strip().split("=")
                    if key == "default_steps":
                        config["default_steps"] = int(value)  # Read as integer
        except Exception as e:
            echo(f"Error reading config file: {e}. Using default configurations.")
    else:
        echo(f"Config file not found: {CONFIG_FILE}. Creating with default values...")
        save_config()


def save_config():
    """
    Saves the current configuration to the prisonconfig.txt file.
    """
    with open(CONFIG_FILE, "w") as f:
        for key, value in config.items():
            f.write(f"{key}={value}\n")



class MiningScriptGUI(ctk.CTk):
    def __init__(self):
        super().__init__()

        self.title("CervUtil")
        self.geometry("240x365")

        # Chat Message Section
        self.chat_label = ctk.CTkLabel(self, text="Chat Message:")
        self.chat_label.pack(pady=5)

        self.chat_entry = ctk.CTkEntry(self)
        self.chat_entry.pack(pady=5)
        self.chat_entry.bind("<Return>", self.send_chat)  # Bind Enter key to send chat

        self.chat_button = ctk.CTkButton(self, text="Send Chat", command=self.send_chat)
        self.chat_button.pack(pady=5)

        # Speed Section (formerly Default Steps)
        self.speed_label = ctk.CTkLabel(self, text=f"Speed: {config['default_steps']}")
        self.speed_label.pack(pady=5)

        # Inverted slider: left for slower, right for faster
        self.speed_slider = ctk.CTkSlider(
            self, from_=50, to=1, number_of_steps=49, command=self.update_speed
        )
        self.speed_slider.set(config["default_steps"])  # Set to current value
        self.speed_slider.pack(pady=5)

        # Toggle Script Section
        self.toggle_button = ctk.CTkButton(
            self,
            text="Start Script",  # Always start with "Start Script"
            command=self.toggle_script,
        )
        self.toggle_button.pack(pady=5)

        # Exit Button
        self.exit_button = ctk.CTkButton(self, text="Exit Script", command=self.exit_script)
        self.exit_button.pack(pady=20)

        # Background Thread for Script Execution
        self.script_thread = None

    def send_chat(self, event=None):
        """
        Sends a chat message or executes a command based on the message input.
        Clears the chat entry after sending the message.
        """
        message = self.chat_entry.get()
        if message:
            if message.startswith("/"):
                execute(message)  # Call execute for commands
                print(f"Command executed: {message}")
            else:
                chat(message)  # Call chat for normal messages
                print(f"Chat message sent: {message}")

            self.chat_entry.delete(0, 'end')  # Clear the chat entry field

    def update_speed(self, value):
        """
        Updates the speed (inverted steps) for the sequence based on the slider value.
        """
        config["default_steps"] = int(value)  # Convert slider value to integer
        self.speed_label.configure(text=f"Speed: {config['default_steps']}")
        save_config()

    def toggle_script(self):
        """
        Toggles the script on and off. Handles starting and stopping gracefully.
        """
        config["script_running"] = not config["script_running"]

        if config["script_running"]:
            self.toggle_button.configure(text="Stop Script")
            print("Script started.")
            self.start_script_thread()
        else:
            self.toggle_button.configure(text="Start Script")
            print("Stopping script.")
            config["script_running"] = False  # Signal the thread to stop
            stop_all_movements()

    def start_script_thread(self):
        """
        Starts the main mining script in a separate thread.
        Safely stops any previous thread before starting a new one.
        """
        if self.script_thread is not None and self.script_thread.is_alive():
            print("Stopping previous thread.")
            config["script_running"] = False  # Signal the thread to stop
            self.script_thread.join(timeout=1)  # Wait for the thread to finish
            if self.script_thread.is_alive():
                print("Thread did not finish in time. Forcing termination.")
                return  # Skip starting a new thread if the previous one is stuck

        # Reset the script_running flag and start a new thread
        config["script_running"] = True
        self.script_thread = threading.Thread(target=auto_mining_loop, daemon=True)
        self.script_thread.start()
        print("New thread started.")

    def exit_script(self):
        """
        Stops the entire script, including GUI and mining thread.
        """
        global terminate_script
        print("Exiting the script...")
        terminate_script = True  # Set the terminate flag
        config["script_running"] = False  # Ensure the mining loop stops
        save_config()
        self.destroy()



OBSTACLES = ["minecraft:bedrock", "minecraft:ladder", "minecraft:shroomlight"]
mouse_speed = 1.0
script_running = False
pitch_direction = -1

def check_for_obstacles_and_turn():
    """
    Continuously checks the block the player is looking at for obstacles and handles them.
    Exits immediately if the script is stopped.
    """
    if not config["script_running"]:
        return

    targeted_block = player_get_targeted_block()

    if targeted_block:
        block_type = targeted_block.type.split("[")[0]
        block_side = targeted_block.side

        # Call handle_mine_reset only for bedrock with side='up'
        if block_type == "minecraft:bedrock" and block_side == "up":
            print("Bedrock with side='up' detected during obstacle check.")
            handle_mine_reset()
            return

        # Handle other obstacles normally
        if block_type in OBSTACLES:
            handle_obstacle()


def handle_mine_reset():
    """
    Handles mine reset logic for bedrock with side='up'.
    Only triggers when necessary, and avoids running on script start.
    """
    if not config["script_running"]:
        print("Script not running. Skipping mine reset.")
        return False  # Exit if the script is stopped

    targeted_block = player_get_targeted_block()
    if targeted_block:
        block_type = targeted_block.type.split("[")[0]
        block_side = targeted_block.side

        # Trigger reset only for bedrock with side='up'
        if block_type == "minecraft:bedrock" and block_side == "up":
            print("Bedrock with side='up' detected. Triggering mine reset.")
            stop_all_movements()
            execute("/mine y")
            time.sleep(5)
            follow_reset_sequence()
            return True

    print("No bedrock with side='up' detected. Continuing mining.")
    return False


def is_block_in_front():
    x, y, z = player_position()
    yaw, current_pitch = player_orientation()

    yaw_radians = math.radians(yaw)
    direction_x = round(-math.sin(yaw_radians))
    direction_z = round(math.cos(yaw_radians))

    def blocks_in_grid():
        for depth in range(1, 4):
            for offset in [-1, 0, 1]:
                for height in [0, 1]:
                    block_x = x + (direction_x * depth) + (direction_z * offset)
                    block_z = z + (direction_z * depth) + (direction_x * offset)
                    block_y = y + height
                    block = getblock(block_x, block_y, block_z)
                    if block and block.strip() != "minecraft:air":
                        return True
        return False

    if blocks_in_grid():
        return True

    current_pitch = player_orientation()[1]
    target_pitch = 30
    steps = 15
    delay = 0.01

    for step in range(steps + 1):
        interpolated_pitch = current_pitch + (step / steps) * (target_pitch - current_pitch)
        player_set_orientation(yaw, interpolated_pitch)
        time.sleep(delay)

    return False

def stop_all_movements():
    """
    Stops all ongoing player actions like forward movement, attacking, etc.
    Safely handles stopping to prevent crashes.
    """
    print("Stopping all movements...")
    player_press_forward(False)
    player_press_attack(False)
    player_press_left(False)
    player_press_right(False)
    player_press_jump(False)
    print("All movements stopped.")

def safe_sleep(seconds):
    """
    Sleeps in small intervals to allow checking script_running flag.
    """
    interval = 0.1  # Sleep in small intervals
    for _ in range(int(seconds / interval)):
        if not config["script_running"]:
            print("Exiting sleep early due to script stop.")
            return
        time.sleep(interval)


def follow_reset_sequence():
    """
    Perform the reset sequence: move forward, detect shroomlight/bedrock, and jump.
    Only runs if the script is active.
    """
    if not config["script_running"]:
        print("Script is not running. Skipping reset sequence.")
        return

    print("Starting reset sequence...")
    config["restart_in_progress"] = True  # Signal reset sequence in progress
    player_press_forward(True)

    while True:
        x, y, z = player_position()
        block_below = getblock(x, y - 1, z)

        if block_below and block_below.strip() in ["minecraft:shroomlight", "minecraft:bedrock"]:
            print(f"Block below detected: {block_below.strip()}. Jumping.")
            player_press_jump(True)
            time.sleep(0.5)
            player_press_jump(False)
            break

    player_press_forward(True)
    time.sleep(3)  # Continue moving forward for 3 seconds
    player_press_forward(False)

    config["restart_in_progress"] = False  # Reset sequence complete
    print("Reset sequence completed. Resuming mining.")
    hold_forward_and_mine()  # Explicitly resume mining


def smooth_look_away():
    #player_press_attack(True)
    current_yaw, _ = player_orientation()
    target_yaw = current_yaw + 180

    steps = 18 
    delay = 0.01
    for step in range(steps + 1):
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        player_set_orientation(interpolated_yaw, 26)
        time.sleep(delay)


def hold_forward_and_mine():
    """
    Mines while holding forward, ensuring continuous motion during cursor movements.
    Stops immediately when the script_running flag is set to False.
    """
    while config["script_running"]:
        if is_block_in_front():
            player_press_forward(True)
            player_press_attack(True)

            # Perform mining and obstacle checks
            start_time = time.time()
            while time.time() - start_time < 1:
                if not config["script_running"]:
                    stop_all_movements()
                    return
                check_for_obstacles_and_turn()
                smooth_cursor_movement()
        else:
            # No blocks detected, move forward while breaking
            player_press_forward(True)
            player_press_attack(True)

        if not config["script_running"]:
            stop_all_movements()
            return
        time.sleep(0.1)

    # Ensure movements are stopped if the loop exits
    stop_all_movements()


def stop_forward_movement():
    player_press_forward(False)


def check_inventory_and_sell():
    inventory = player_inventory()
    for item in inventory:
        if item.slot == 35:
            execute_sellall_nonblocking()
            break


def execute_sellall_nonblocking():
    execute("/sellall")
    for _ in range(20):
        time.sleep(0.1)
        check_for_obstacles_and_turn()


def smooth_cursor_movement():
    """
    Perform smooth cursor movement while respecting the script's running state.
    Ensures no delay before and after the snap movement with pitch adjustments.
    """
    current_yaw, current_pitch = player_orientation()

    # Define the movement sequence
    sequence = [
        {"yaw_offset": 27.5, "pitch_offset": 6, "steps": max(1, config["default_steps"] - 2), "delay": 0.006},
        {"yaw_offset": -27.5, "pitch_offset": 6, "steps": max(1, config["default_steps"] - 2), "delay": 0.006},
        {"yaw_offset": -60, "pitch_offset": 0, "steps": max(1, config["default_steps"] + 5), "delay": 0.008},
        {"yaw_offset": -27.5, "pitch_offset": -6, "steps": max(1, config["default_steps"] - 2), "delay": 0.006},
        {"yaw_offset": 27.5, "pitch_offset": -6, "steps": max(1, config["default_steps"] - 2), "delay": 0.006},
        {"yaw_offset": 60, "pitch_offset": 14 - current_pitch, "steps": max(1, config["default_steps"] + 5), "delay": 0.00001},  # Snap movement
    ]

    # Perform the sequence
    for i, movement in enumerate(sequence):
        if not config["script_running"]:  # Stop immediately if the script is not running
            return

        target_yaw = current_yaw + movement["yaw_offset"]
        target_pitch = current_pitch + movement["pitch_offset"]
        steps = max(1, movement["steps"])
        delay = movement["delay"]

        # Smoothly transition to the target yaw and pitch
        for step in range(steps + 1):
            if not config["script_running"]:
                return

            interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
            interpolated_pitch = current_pitch + (step / steps) * (target_pitch - current_pitch)
            player_set_orientation(interpolated_yaw, interpolated_pitch)

            # Skip delay for snap movement
            if movement["pitch_offset"] == 14 - current_pitch:
                continue

            # Apply delay for other movements
            if step < steps and delay > 0:
                time.sleep(delay)

        # Update the current yaw and pitch for the next movement
        current_yaw = target_yaw
        current_pitch = target_pitch

        # Check for obstacles after completing each movement except the snap movement
        if movement["pitch_offset"] != 14 - current_pitch:
            check_for_obstacles_and_turn()




def obstacle_detected():
    targeted_block = player_get_targeted_block()
    if targeted_block:
        block_type = targeted_block.type.split("[")[0]
        if block_type in OBSTACLES:
            return True
    return False


def handle_obstacle():
    """
    Handles detected obstacles by stopping movements and turning away.
    Avoids 180-degree turns during or immediately after reset sequences.
    """
    if config.get("restart_in_progress", False):
        return  # Skip obstacle handling if a reset is in progress

    print("Obstacle detected. Stopping and turning.")
    stop_all_movements()
    current_yaw, current_pitch = player_orientation()
    turn_180(current_yaw, current_pitch)  # Perform a 180-degree turn
    hold_forward_and_mine()  # Resume mining



def turn_180(current_yaw, current_pitch):
    target_yaw = current_yaw + 180

    steps = 18
    delay = 0.01
    for step in range(steps + 1):
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        player_set_orientation(interpolated_yaw, current_pitch)
        time.sleep(delay)


def auto_mining_loop():
    """
    Main loop for auto-mining. Handles clean exit when the script is stopped.
    """
    try:
        print("Starting auto-mining loop.")
        while config["script_running"]:
            print("Mining loop iteration...")
            player_press_forward(True)
            player_press_attack(True)

            # Handle resets and obstacles
            if handle_mine_reset():
                print("Mine reset triggered.")
                continue

            check_for_obstacles_and_turn()
            check_inventory_and_sell()
            smooth_cursor_movement()

            if not config["script_running"]:  # Double-check before sleep
                print("Script running flag set to False. Exiting loop.")
                break

            time.sleep(0.05)  # Short delay for rapid checks
    except Exception as e:
        print(f"Error in auto_mining_loop: {e}")
    finally:
        print("Exiting auto-mining loop.")
        stop_all_movements()


if __name__ == "__main__":
    read_config()  # Load configuration

    # Always start with the script turned off
    config["script_running"] = False

    # Initialize the GUI
    app = MiningScriptGUI()

    # Start the GUI main loop
    app.mainloop()





