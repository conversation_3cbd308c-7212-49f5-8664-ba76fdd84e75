import minescript
import time
import re
import http.client
import json
import os
from datetime import datetime
import pytz  # Importing pytz for timezone handling

# Your updated Discord webhook URL
DISCORD_WEBHOOK_URL = "/api/webhooks/1294588808645574677/m6p3DWfAnV9yUyDFevpRGXRUW8ouGiicQmzEC6odLFuRQusAS8P_vUsBzB1Rt1HnM1t-"
DISCORD_HOST = "discord.com"

# Set the timezone to PST (Pacific Standard Time)
PST = pytz.timezone('America/Los_Angeles')

# List of words/phrases to exclude from the output
excluded_phrases = [
    "SOLD", "Building Blocks", "Decorations", "Redstone", "Transport", "Food", "Tools", 
    "Combat", "Brewing", "Runes", "Crate Keys", "Enchanted Books", "Tag Vouchers", 
    "Mob Spawners", "Contraband", "Furniture"
]

def send_to_discord_embed(output):
    """Send an embed message to <PERSON>rd via webhook using http.client."""
    
    # Check if any excluded phrase exists in the output; if so, do not send
    if any(phrase in output for phrase in excluded_phrases):
        return  # Exit if any excluded phrase is found

    # Get the current time in PST
    current_time = datetime.now(PST)

    # Create the embed structure
    embed = {
        "title": "Auction House",
        "color": 0x00b0f4,
        "timestamp": current_time.isoformat(),  # Using ISO format for the timestamp in PST
        "fields": [
            {
                "name": "",
                "value": f"```{output}```",
                "inline": False
            }
        ],
        "thumbnail": {
            "url": "https://preview.redd.it/blursed-tennis-ball-bird-v0-3ldo99m5tpv91.jpg?width=1080&crop=smart&auto=webp&s=719cfe68cb5f95af0bc0f0b3be7ad4f2c5cdce80"
        },
        "footer": {
            "icon_url": "https://preview.redd.it/blursed-tennis-ball-bird-v0-3ldo99m5tpv91.jpg?width=1080&crop=smart&auto=webp&s=719cfe68cb5f95af0bc0f0b3be7ad4f2c5cdce80",
            "text": "Auction Updates"
        }
    }

    data = {
        "embeds": [embed]  # Discord expects embeds in a list
    }

    # Convert data to JSON format
    json_data = json.dumps(data)

    # Set up the connection to Discord
    conn = http.client.HTTPSConnection(DISCORD_HOST)
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "AuctionBot"
    }

    # Send the POST request to the webhook URL
    conn.request("POST", DISCORD_WEBHOOK_URL, body=json_data, headers=headers)
    response = conn.getresponse()

    # Check the response status
    if response.status != 204:
        minescript.echo(f"Failed to send message to Discord. Status Code: {response.status}")
    conn.close()

def extract_text_value(item_str):
    """Extract and clean the part of the item details that contains the dynamic value of 'text', combining single letters into words and ensuring 'Auction Details', 'Price', and 'Poster' are at the end with their values."""
    # Regular expression to find the "text":"<value>" pattern and capture the dynamic text, including empty fields
    pattern = r'"text":"([^"]*)"'  # This captures the value inside the "text":"<value>", including empty strings
    matches = re.findall(pattern, item_str)

    # Initialize a list to store the combined words and special fields for reordering
    words = []
    auction_details = ''
    price = ''
    poster = ''
    current_word = []
    capture_next_text = None  # Track if we need to attach the next text to a specific field (e.g., Price or Poster)

    price_found = None  # Variable to store the parsed price value

    for match in matches:
        # Remove "?" symbols without affecting the rest of the line
        match = match.replace("?", "")  # Remove only "?" from the current match

        if match:  # If the match has content (not empty), it's part of a word
            if "Auction Details" in match:
                auction_details = match  # Store Auction Details
            elif "Price" in match:
                price = match  # Store Price label
                capture_next_text = 'Price'  # Flag to capture the next text as part of the price
            elif "Poster" in match:
                poster = match  # Store Poster label
                capture_next_text = 'Poster'  # Flag to capture the next text as part of the poster
            elif capture_next_text == 'Price' and "Price" not in match:
                price += f" {match}"  # Attach the next text to the Price label (prevent duplication)
                try:
                    price_value = re.findall(r'\d+', match.replace(",", ""))  # Extract numerical price
                    if price_value:
                        price_found = int(price_value[0])  # Convert to integer
                except ValueError:
                    price_found = None
                capture_next_text = None  # Reset after capturing
            elif capture_next_text == 'Poster' and "Poster" not in match:
                poster += f" {match}"  # Attach the next text to the Poster label
                capture_next_text = None  # Reset after capturing
            else:
                current_word.append(match)
        else:  # If the match is empty, we treat it as a word separator
            if current_word:  # If we have collected letters for a word, join them and add to words list
                words.append(''.join(current_word))
                current_word = []  # Reset for the next word

    # If the price is below 10 GC, do not send to Discord
    if price_found is not None and price_found < 10:
        return None  # Return None to signal this should not be sent

    # Add the final word if there are any remaining letters
    if current_word:
        words.append(''.join(current_word))

    # Exclude specific unwanted phrases and time-related information
    excluded_phrases_internal = [
        "Expires In", "Category", "Miscellaneous", "Left Click", "to", "Purchase", "Right Click", "GC)", "DELETED"
    ]
    
    # Filter out any words that match excluded phrases or time-related terms (days/hours)
    filtered_words = [word for word in words if 'days' not in word and 'hours' not in word and all(phrase not in word for phrase in excluded_phrases_internal)]

    # If there are more than 4 words, take the 4th-to-last and move it to the top
    if len(filtered_words) >= 4:
        fourth_to_last = filtered_words[-4]
        filtered_words = [fourth_to_last] + filtered_words[:len(filtered_words) - 4] + filtered_words[len(filtered_words) - 3:]

    # Append the reserved fields (Auction Details, Price, Poster) at the end in the specified order
    if auction_details:
        filtered_words.append(auction_details)
    if price:
        filtered_words.append(price)
    if poster:
        filtered_words.append(poster)

    # Return the final combined output as a single string
    return '\n'.join(filtered_words) if filtered_words else "No valid 'text' field found"

def main():
    # Initialize a variable to track the previous item in slot 0
    previous_item = None
    last_ah_check = time.time()

    # Continuously check for updates in slot 0
    while True:
        # Every 60 seconds, ensure the Auction House is open
        current_time = time.time()
        if current_time - last_ah_check >= 60:
            container_items = minescript.container_get_items()
            
            # If the container is not open or items are not available, open the Auction House
            if container_items is None or not any(item.slot == 0 for item in container_items):
                minescript.execute("/ah")
            last_ah_check = current_time

        # Retrieve all items in the container
        container_items = minescript.container_get_items()

        # Check if container_items is None
        if container_items is None:
            time.sleep(2)
            continue

        # Check if there is an item in slot 0
        for item in container_items:
            if item.slot == 0:  # Check if the item is in slot 0
                # Convert the item to a string and extract the 'text' values
                item_str = str(item)
                text_values = extract_text_value(item_str)
                
                # If the item is excluded due to price, skip it
                if text_values is None:
                    continue

                # Check if the current item is different from the previous one
                if previous_item != text_values:
                    # Echo the new item details to Discord embed in a single field
                    send_to_discord_embed(text_values)  # Send combined text as an embed to Discord webhook

                    # Update the previous item to the current one
                    previous_item = text_values
                break

        # Wait before checking again (adjust the sleep duration as needed)
        time.sleep(2)

if __name__ == "__main__":
    main()
