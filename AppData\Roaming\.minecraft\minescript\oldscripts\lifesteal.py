import minescript
import time
import discord
import asyncio
from datetime import datetime

# Discord Bot Setup
DISCORD_TOKEN = "MTA3MTU3OTczNTY4MDg3NjYxNg.Gc2eea.dXzkOxWZQnyKn-5tvEfK4nMJEvI0nUiXJwK69M"
CHANNEL_ID = 1316631134326624338  # Replace with your channel ID

# Define area coordinates (X and Z only)
pond_coords = (65, -32, 123, 20)
outpost_coords = (-17, -83, 17, -47)

# Initialize Discord client
intents = discord.Intents.default()
intents.messages = True
client = discord.Client(intents=intents)

async def update_embed(embed_message, pond_players, outpost_players, nearby_players):
    last_updated = datetime.now()

    while True:
        # Calculate elapsed time
        elapsed_time = datetime.now() - last_updated
        minutes, seconds = divmod(int(elapsed_time.total_seconds()), 60)
        footer_text = f"Last updated - {minutes}m {seconds}s ago"

        # Create Embed
        embed = discord.Embed(
            title="wz thugs",
            colour=0x00b0f4
        )

        # Add embed fields
        embed.add_field(
            name="Players at pond",
            value="\n".join(pond_players or ["No players found"]),
            inline=False
        )

        embed.add_field(
            name="Players at outpost",
            value="\n".join(outpost_players or ["No players found"]),
            inline=False
        )

        embed.add_field(
            name="Nearby players",
            value="\n".join(nearby_players or ["No nearby players"]),
            inline=False
        )

        embed.set_thumbnail(
            url="https://preview.redd.it/blursed-tennis-ball-bird-v0-3ldo99m5tpv91.jpg?width=640&crop=smart&auto=webp&s=13979e5501939d15961e91e28bcf6005e9e9b224"
        )
        embed.set_footer(text=footer_text)

        # Edit the embed message
        await embed_message.edit(embed=embed)

        # Update every 5 seconds
        await asyncio.sleep(5)

def check_area(x1, z1, x2, z2, label):
    min_x, max_x = sorted([x1, x2])
    min_z, max_z = sorted([z1, z2])

    # Check for players in the defined area
    players_in_area = minescript.players(
        position=(min_x, 0, min_z),
        offset=(max_x - min_x, 256, max_z - min_z)
    )
    player_names = [player.name for player in players_in_area]
    minescript.echo(f"Players in {label}: {', '.join(player_names) or 'No players'}")
    return player_names

def get_all_nearby_players():
    # Detect all players without a radius limit
    nearby_players = minescript.players(position=(0, 0, 0), offset=(30000000, 256, 30000000))
    player_names = [player.name for player in nearby_players]
    minescript.echo(f"Nearby players: {', '.join(player_names) or 'None'}")
    return player_names

def wait_for_world_load():
    x, y, z = [int(coord) for coord in minescript.player_position()]
    minescript.await_loaded_region(x - 50, z - 50, x + 50, z + 50)
    minescript.echo("World loaded")

async def lifesteal_sequence(embed_message):
    # Execute /lifesteal
    minescript.execute("/lifesteal")
    minescript.echo("Executed /lifesteal")

    # Add a delay after executing /lifesteal
    time.sleep(3)

    # Wait for the world to load
    wait_for_world_load()

    # Check the block below the player
    x, y, z = [int(coord) for coord in minescript.player_position()]
    block_below = minescript.getblock(x, y - 1, z)
    minescript.echo(f"Block below is {block_below}")

    if block_below == "minecraft:sea_lantern":
        # Execute /home checker if in spawn
        minescript.execute("/home checker")
        minescript.echo("Executed /home checker")
        wait_for_world_load()
        time.sleep(5)

    # Check both areas for players
    pond_players = check_area(*pond_coords, "Pond")
    outpost_players = check_area(*outpost_coords, "Outpost")

    # Get all nearby players (no radius limit)
    nearby_players = get_all_nearby_players()

    # Execute /hub after checking areas
    minescript.execute("/hub")
    minescript.echo("Executed /hub")

    # Update embed and timer
    asyncio.create_task(update_embed(embed_message, pond_players, outpost_players, nearby_players))

async def main_loop():
    channel = client.get_channel(CHANNEL_ID)

    # Initialize the embed
    embed_message = await channel.send(embed=discord.Embed(title="Initializing..."))

    while True:
        try:
            await lifesteal_sequence(embed_message)
            await asyncio.sleep(300)  # Wait 5 minutes
        except Exception as e:
            minescript.echo(f"Error: {e}")

@client.event
async def on_ready():
    print(f"Logged in as {client.user}")
    asyncio.create_task(main_loop())

client.run(DISCORD_TOKEN)
