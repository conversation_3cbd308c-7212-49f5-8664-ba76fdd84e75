import minescript
import time

def place_and_harvest_crops():
    while True:
        minescript.player_press_use(True)
        # Check the block the player is looking at
        targeted_block = minescript.player_get_targeted_block(max_distance=5)
        
        # Ensure the structure is as expected and check for fully grown carrots
        if targeted_block and isinstance(targeted_block, list) and len(targeted_block) >= 4:
            block_name = targeted_block[3]
            
            # If the block is fully grown carrots, break it
            if block_name == "minecraft:carrots[age=7]":
                minescript.player_press_attack(True)
                time.sleep(0.1)  # Short delay for breaking action
                minescript.player_press_attack(False)
        
        # Add a slight delay to avoid overwhelming the game with commands
        time.sleep(0.2)

# Run the function
place_and_harvest_crops()
