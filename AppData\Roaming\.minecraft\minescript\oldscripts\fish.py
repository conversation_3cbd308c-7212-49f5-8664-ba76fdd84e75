import time
import pyautogui
import minescript

def run_script():
    # Execute /is command
    minescript.execute("/is")
    
    # Wait 100ms
    time.sleep(0.1)
    
    # Click at (1065, 466)
    pyautogui.click(1065, 466)
    
    # Wait 300ms
    time.sleep(0.3)
    
    # Click at (924, 447)
    pyautogui.click(924, 447)
    
    # Wait 300ms
    time.sleep(0.3)
    
    # Click at (996, 446)
    pyautogui.click(996, 446)
    
    # Wait 500ms
    time.sleep(0.5)
    
    # Set player orientation to look at (2, 99, 0)
    minescript.player_set_orientation(2, 99)
    
    # Wait 100ms
    time.sleep(0.1)
    
    # Hold left key for 500ms
    minescript.player_press_left(True)
    time.sleep(0.5)
    minescript.player_press_left(False)
    
    # Hold W until the player is looking at water
    minescript.player_press_forward(True)
    
    while True:
        targeted_block = minescript.player_get_targeted_block(max_distance=20)
        if targeted_block and targeted_block['type'] == 'water':
            minescript.player_press_forward(False)
            break
        time.sleep(0.1)

if __name__ == "__main__":
    run_script()
