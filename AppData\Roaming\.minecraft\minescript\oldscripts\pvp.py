#made by Cerv 😈
#discord - itscerv

import time
import re
import threading
import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
from draw_text import draw_centered_string
from minescript import EventQueue, EventType
import minescript  # Importing minescript for in-game messages
import os
import queue  # Import the queue module

# Path to the config file
CONFIG_FILE_PATH = os.path.expandvars(r'minescript\pvpconfig.txt')

# Global variables to store chat messages and their text objects
chat_messages = []
chat_text_objects = []
runes_chat_messages = []
runes_chat_text_objects = []
chat_game_messages = []
chat_game_text_objects = []
show_rune_messages = True  # Toggle to show or hide rune messages
show_runic_obstruction = True  # Toggle to show or hide Runic Obstruction messages
show_anc_helm_proc = True  # Toggle to show or hide Anc Helm proc messages
show_dasher_message = True  # Toggle to show or hide Dasher cooldown messages
show_static_disarm_message = True  # Toggle to show or hide Static Disarm messages
show_honorable_blade_message = True  # Toggle to show or hide Honorable Blade messages
test_message_displayed = False  # Flag to track the visibility of the test message
test_message_object = None  # Store the text object for the test message
last_update_time = time.time()  # Track time for message updates
show_chat_game_message = True  # Toggle to show or hide chat game messages
chat_game_timer_active = False  # Flag to indicate if the chat game timer is active
chat_game_timer_duration = 300  # 5-minute countdown timer (300 seconds)
chat_game_start_time = 0  # Store the start time of the chat game
runes_message_x = 238  # Default X position for Runes messages
runes_message_y = 80  # Default Y position for Runes messages
chat_game_x = 47  # Default X position for Chat Game messages
chat_game_y = 15  # Default Y position for Chat Game messages
current_chat_game_message = "Waiting for a chat game to start"


# Cooldown times for each Honorable Blade tier
honorable_blade_cooldowns = {
    "I": 60,
    "II": 55,
    "III": 50,
    "IV": 45,
    "V": 40
}

# Duration times for each Ancient Helm tier
anc_helm_durations = {
    "IV": 7,  # T4
    "V": 9   # T5
}


# Regular expressions to extract information from the messages
pattern_1 = re.compile(r"RUNES.*You have disabled the rune effects of (.*) with your Runic Obstruction .* for (\d+) seconds.")
pattern_2 = re.compile(r"RUNES.*(.*) has prevented your custom enchants from working for (\d+) seconds with their Runic Obstruction .*.")
pattern_anc_helm = re.compile(r"RUNES.*(.*) has a combo of 3 on you! Your Ra's Wrath (IV|V) has activated!")
pattern_negative_anc_helm = re.compile(r"RUNES.*Your combo of 3 on (.*) has activated their Ra's Wrath (IV|V)!")
pattern_dasher = re.compile(r"RUNES.*Dashed! Your Dasher (.*) is usable again in: (\d+)s")
pattern_static_disarm = re.compile(r"RUNES.*(.*)'s Static Disarm (.*) reduced your attack speed by (\d+)% for (\d+) seconds")
pattern_honorable_blade = re.compile(r"RUNES.*You have dashed forward using your Honorable Blade (.*)!")
pattern_chat_game = re.compile(r"! (.*) got the answer (.*) in (\d+) seconds!")

# Store the last time the countdown was updated
last_update_time = time.time()

# Config file management functions
def read_config():
    """Read configuration from pvpconfig.txt."""
    global runes_message_x, runes_message_y, chat_game_x, chat_game_y
    global show_rune_messages, show_chat_game_message, show_runic_obstruction
    global show_anc_helm_proc, show_dasher_message, show_static_disarm_message, show_honorable_blade_message

    if os.path.exists(CONFIG_FILE_PATH):
        with open(CONFIG_FILE_PATH, 'r') as file:
            for line in file:
                key, value = line.strip().split('=')
                if key == 'runes_message_x':
                    runes_message_x = int(value)
                elif key == 'runes_message_y':
                    runes_message_y = int(value)
                elif key == 'chat_game_x':
                    chat_game_x = int(value)
                elif key == 'chat_game_y':
                    chat_game_y = int(value)
                elif key == 'show_rune_messages':
                    show_rune_messages = value.lower() == 'true'
                elif key == 'show_chat_game_message':
                    show_chat_game_message = value.lower() == 'true'
                elif key == 'show_runic_obstruction':
                    show_runic_obstruction = value.lower() == 'true'
                elif key == 'show_anc_helm_proc':
                    show_anc_helm_proc = value.lower() == 'true'
                elif key == 'show_dasher_message':
                    show_dasher_message = value.lower() == 'true'
                elif key == 'show_static_disarm_message':
                    show_static_disarm_message = value.lower() == 'true'
                elif key == 'show_honorable_blade_message':
                    show_honorable_blade_message = value.lower() == 'true'
    else:
        save_config()  # If config doesn't exist, create it with default values

def save_config():
    """Save current configuration to pvpconfig.txt."""
    config_dir = os.path.dirname(CONFIG_FILE_PATH)
    os.makedirs(config_dir, exist_ok=True)

    with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as file:
        file.write(f"runes_message_x={runes_message_x}\n")
        file.write(f"runes_message_y={runes_message_y}\n")
        file.write(f"chat_game_x={chat_game_x}\n")
        file.write(f"chat_game_y={chat_game_y}\n")
        file.write(f"show_rune_messages={show_rune_messages}\n")
        file.write(f"show_chat_game_message={show_chat_game_message}\n")
        file.write(f"show_runic_obstruction={show_runic_obstruction}\n")
        file.write(f"show_anc_helm_proc={show_anc_helm_proc}\n")
        file.write(f"show_dasher_message={show_dasher_message}\n")
        file.write(f"show_static_disarm_message={show_static_disarm_message}\n")
        file.write(f"show_honorable_blade_message={show_honorable_blade_message}\n")

# Update and display the test message
def toggle_test_message():
    global test_message_displayed, test_message_object

    if test_message_displayed:
        # Remove the test message if it's displayed
        if test_message_object and test_message_object in runes_chat_text_objects:
            runes_chat_text_objects.remove(test_message_object)
        if test_message_object:  # Only clear the text if the object exists
            test_message_object.string.set_value("")  # Clear the text
        test_message_displayed = False
    else:
        # Show the test message if it's not displayed
        test_message_object = display_runes_message("Test message", 0x00ff00, 5)  # Use display_runes_message instead of display_message
        test_message_displayed = True

def update_runes_message_x(value):
    global runes_message_x
    runes_message_x = int(float(value))
    save_config()

def update_runes_message_y(value):
    global runes_message_y
    runes_message_y = int(float(value))
    save_config()

def update_chat_game_x(value):
    global chat_game_x
    chat_game_x = int(float(value))
    save_config()

def update_chat_game_y(value):
    global chat_game_y
    chat_game_y = int(float(value))
    save_config()

def toggle_rune_messages():
    global show_rune_messages
    show_rune_messages = not show_rune_messages
    save_config()

def toggle_chat_game_message():
    global show_chat_game_message, chat_game_messages, chat_game_text_objects

    show_chat_game_message = not show_chat_game_message
    save_config()

    if not show_chat_game_message:
        # Clear chat game messages when toggled off
        if chat_game_messages and chat_game_text_objects:
            chat_game_messages.clear()
            chat_game_text_objects.clear()


# Other toggle functions
def toggle_runic_obstruction():
    global show_runic_obstruction
    show_runic_obstruction = not show_runic_obstruction
    save_config()

def toggle_anc_helm_proc():
    global show_anc_helm_proc
    show_anc_helm_proc = not show_anc_helm_proc
    save_config()

def toggle_dasher_message():
    global show_dasher_message
    show_dasher_message = not show_dasher_message
    save_config()

def toggle_static_disarm_message():
    global show_static_disarm_message
    show_static_disarm_message = not show_static_disarm_message
    save_config()

def toggle_honorable_blade_message():
    global show_honorable_blade_message
    show_honorable_blade_message = not show_honorable_blade_message
    save_config()


def filter_and_format_message(message):
    global current_chat_game_message

    match_chat_game = pattern_chat_game.search(message)
    # Check if we want to show rune-related messages
    if not show_rune_messages:
        if pattern_1.search(message) or pattern_2.search(message):
            return None
    
    # Check if we want to show Runic Obstruction messages
    if not show_runic_obstruction:
        if pattern_1.search(message) or pattern_2.search(message):
            return None

    # Check if we want to show Ancient Helm proc messages
    if not show_anc_helm_proc:
        if pattern_anc_helm.search(message) or pattern_negative_anc_helm.search(message):
            return None

    # Check if we want to show Dasher cooldown messages
    if not show_dasher_message:
        if pattern_dasher.search(message):
            return None

    # Check if we want to show Static Disarm messages
    if not show_static_disarm_message:
        if pattern_static_disarm.search(message):
            return None

    # Check if we want to show Honorable Blade messages
    if not show_honorable_blade_message:
        if pattern_honorable_blade.search(message):
            return None

    # Now proceed with formatting messages if the toggles are ON
    match_1 = pattern_1.search(message)
    match_2 = pattern_2.search(message)
    match_dasher = pattern_dasher.search(message)
    match_static_disarm = pattern_static_disarm.search(message)
    match_anc_helm = pattern_anc_helm.search(message)  # Positive Anc Helm proc
    match_negative_anc_helm = pattern_negative_anc_helm.search(message)  # Negative Anc Helm proc
    match_honorable_blade = pattern_honorable_blade.search(message)
    match_chat_game = pattern_chat_game.search(message)

    if match_1:
        name, time_duration = match_1.groups()
        time_duration = int(time_duration)
        return (f"{name} is RUNICED for {time_duration} seconds", 0x00ff00, time_duration)

    elif match_2:
        name, time_duration = match_2.groups()
        time_duration = int(time_duration)
        return (f"You are RUNICED for {time_duration} seconds", 0xff0000, time_duration)

    elif match_anc_helm:
        name, tier = match_anc_helm.groups()
        time_duration = anc_helm_durations.get(tier, 7)  # Default to 7 seconds
        return (f"Ancient Helm procced on {name} for {time_duration} seconds", 0x00ff00, time_duration)

    elif match_negative_anc_helm:
        name, tier = match_negative_anc_helm.groups()
        time_duration = anc_helm_durations.get(tier, 7)  # Default to 7 seconds
        return (f"{name}'s Ancient Helm procced on you for {time_duration} seconds", 0xff0000, time_duration)

    elif match_dasher:
        tier, time_left = match_dasher.groups()
        time_left = int(time_left)
        return (f"You can use Dasher again in {time_left} seconds", 0x00ff00, time_left)

    elif match_static_disarm:
        name, tier, percent, time_left = match_static_disarm.groups()
        time_left = int(time_left)
        return (f"{name} reduced your attack speed by {percent}% for {time_left} seconds", 0xff0000, time_left)
    if match_honorable_blade:
        tier = match_honorable_blade.group(1)  # Get the Roman numeral (tier)
        cooldown = honorable_blade_cooldowns.get(tier, 60)  # Default to 60 seconds if tier not found
        return (f"You can use Imp Sword again in {cooldown} seconds", 0x00ff00, cooldown)

    return None

def remove_duplicate_message(message, message_list, text_object_list):
    """Remove an old duplicate message from the given message list and text object list."""
    # Extract the base part of the message (before "for x seconds") to compare
    base_message = message.split('for')[0]

    # Check if the message with the same base content already exists in the given list
    for i, (msg, color, time_left) in enumerate(message_list):
        if base_message in msg:
            # Remove the old message and text object
            message_list.pop(i)
            text_object_list.pop(i)
            break


def display_runes_message(msg, color, time_left):
    """Display a new message for Runes-related messages on the screen."""
    global runes_chat_text_objects

    # Calculate the position for the new Runes message
    y = runes_message_y - (len(runes_chat_messages) - 1) * 5
    scale = 2

    # Draw a new centered message for Runes
    text_obj = draw_centered_string(msg, x=runes_message_x, y=y, color=color, scale=scale)
    runes_chat_text_objects.append(text_obj)
    return text_obj

# Display Chat Game message
def display_chat_game_message(msg, color, time_left):
    """Display a new message for Chat Game messages on the screen."""
    global chat_game_text_objects

    # Only display the message if chat game messages are enabled
    if show_chat_game_message:
        # Calculate the position for the new Chat Game message
        y = chat_game_y  # Display at chat game's Y position
        scale = 2

        # Draw the centered message for chat games
        text_obj = draw_centered_string(msg, x=chat_game_x, y=y, color=color, scale=scale)
        chat_game_text_objects.append(text_obj)
        return text_obj


def update_runes_messages():
    """Update the countdown for each Runes message and remove messages when the timer hits zero."""
    global runes_chat_messages, runes_chat_text_objects, last_update_time

    # Get the current time
    current_time = time.time()

    # Calculate how much time has passed since the last update
    time_elapsed = current_time - last_update_time

    if time_elapsed >= 1:  # Only update the countdown once a full second has passed
        # Iterate over Runes messages in reverse to avoid issues when removing items
        for i in range(len(runes_chat_messages) - 1, -1, -1):
            msg, color, time_left = runes_chat_messages[i]

            # Reduce the time left by the time elapsed
            time_left -= int(time_elapsed)

            if time_left <= 0:
                # Remove the message if time hits zero
                runes_chat_messages.pop(i)
                runes_chat_text_objects.pop(i)
            else:
                # Update the message with the new time left
                updated_message = re.sub(r"in \d+ seconds", f"in {time_left} seconds", msg)  # Dasher-style countdown
                updated_message = re.sub(r"for \d+ seconds", f"for {time_left} seconds", updated_message)  # Other messages

                # Ensure the new message is stored in the list and displayed
                runes_chat_messages[i] = (updated_message, color, time_left)

                # Update the text object on screen to display the new message with updated time
                runes_chat_text_objects[i].string.set_value(updated_message)

        # Reset the last update time
        last_update_time = current_time

def update_chat_game_messages():
    """Update the countdown for each Chat Game message and remove messages when the timer hits zero."""
    global chat_game_messages, chat_game_text_objects, last_update_time

    # Get the current time
    current_time = time.time()
    
    # Calculate how much time has passed since the last update
    time_elapsed = current_time - last_update_time
    if chat_game_timer_active:
        update_chat_game_timer()  # Update the timer display
    if time_elapsed >= 1:
        for i in range(len(chat_game_messages) - 1, -1, -1):
            msg, color, time_left = chat_game_messages[i]
            time_left -= int(time_elapsed)

            if time_left <= 0:
                # Remove expired message
                chat_game_messages.pop(i)
                chat_game_text_objects.pop(i)
            else:
                # Update the countdown message and ensure it's displayed
                updated_message = current_chat_game_message  # Use the global chat game message (with countdown)
                chat_game_messages[i] = (updated_message, color, time_left)
                if chat_game_text_objects:
                    chat_game_text_objects[i].string.set_value(updated_message)

        last_update_time = current_time

def on_chat_message(message):
    """Handle chat messages for both Runes and Chat Games, format them, and update the rendered text."""
    global runes_chat_messages, chat_game_messages, chat_game_text_objects

    # Process and filter the message for Runes
    formatted_runes_message = filter_and_format_message(message)
    if formatted_runes_message and show_rune_messages:
        msg, color, time_left = formatted_runes_message
        remove_duplicate_message(msg, runes_chat_messages, runes_chat_text_objects)  # Remove duplicate from Runes
        runes_chat_messages.append(formatted_runes_message)
        display_runes_message(msg, color, time_left)

    # Process and filter the message for Chat Games
    formatted_chat_game_message = filter_and_format_chat_game_message(message)
    if formatted_chat_game_message and show_chat_game_message:
        # Ensure only one countdown message exists
        if chat_game_messages and chat_game_text_objects:
            chat_game_messages.clear()  # Clear any previous countdown messages
            chat_game_text_objects.clear()

        msg, color, time_left = formatted_chat_game_message
        chat_game_messages.append(formatted_chat_game_message)
        display_chat_game_message(msg, color, time_left)


def filter_and_format_chat_game_message(message):
    """Filter the message for chat games and format the output."""
    match_chat_game = pattern_chat_game.search(message)
    if match_chat_game and show_chat_game_message:
        name, answer, time_taken = match_chat_game.groups()

        # Clear any active timers and start a fresh countdown
        start_chat_game_timer()

        # Only one active game message, so no need to return a new visible message right away.
        return (f"", 0x00ff00, 5)

    return None

# Function to start a chat game timer
def start_chat_game_timer():
    global chat_game_start_time, chat_game_timer_active, chat_game_messages, chat_game_text_objects, current_chat_game_message

    # Stop any existing timer before starting a new one
    chat_game_timer_active = False  # Stop any active timer
    
    # Clear all existing chat game messages (both green and orange)
    if chat_game_messages and chat_game_text_objects:
        chat_game_messages.clear()
        chat_game_text_objects.clear()

    # Start the new chat game timer
    chat_game_start_time = time.time()
    chat_game_timer_active = True

# Function to update the chat game timer
def update_chat_game_timer():
    global chat_game_timer_active, chat_game_timer_duration, chat_game_start_time, chat_game_messages, current_chat_game_message

    if chat_game_timer_active:
        time_elapsed = time.time() - chat_game_start_time
        remaining_time = chat_game_timer_duration - int(time_elapsed)

        if remaining_time <= 0:
            # Timer finished
            chat_game_timer_active = False
            current_chat_game_message = "Waiting for a chat game to start"
            # Clear previous countdown and display the waiting message
            if chat_game_messages and chat_game_text_objects:
                chat_game_messages.clear()
                chat_game_text_objects.clear()
            display_chat_game_message(current_chat_game_message, 0xffd700, 0)
        else:
            # Format the countdown as MM:SS
            minutes = remaining_time // 60
            seconds = remaining_time % 60
            countdown_message = f"Next chat game is in {minutes}:{seconds:02d}"

            # Update the countdown on screen
            if chat_game_messages:
                chat_game_messages[0] = (countdown_message, 0xffd700, remaining_time)  # Update the first message
                chat_game_text_objects[0].string.set_value(countdown_message)  # Update the displayed text

# Function to create the GUI
def create_gui():
    """Create a Tkinter GUI to adjust values."""
    root = tk.Tk()
    root.title("Cerv's Utils")

    # Create a notebook (tabs) for different sections (Runes, Chat Games)
    notebook = ttk.Notebook(root)
    notebook.pack(expand=True)

    # ======= Runes Tab ========
    runes_tab = ttk.Frame(notebook)
    notebook.add(runes_tab, text="Runes")

    # Runes X and Y Position controls
    runes_message_x_label = ttk.Label(runes_tab, text="Runes Message X Position:")
    runes_message_x_label.pack()

    runes_message_x_value = tk.Label(runes_tab, text=f"{runes_message_x}")
    runes_message_x_value.pack()

    runes_message_x_slider = ttk.Scale(runes_tab, from_=0, to=500, orient='horizontal', length=400,
                                    command=lambda value: [update_runes_message_x(value), runes_message_x_value.config(text=f"{int(float(value))}")])
    runes_message_x_slider.set(runes_message_x)
    runes_message_x_slider.pack()

    runes_message_y_label = ttk.Label(runes_tab, text="Runes Message Y Position:")
    runes_message_y_label.pack()

    runes_message_y_value = tk.Label(runes_tab, text=f"{runes_message_y}")
    runes_message_y_value.pack()

    runes_message_y_slider = ttk.Scale(runes_tab, from_=0, to=200, orient='horizontal', length=400,
                                    command=lambda value: [update_runes_message_y(value), runes_message_y_value.config(text=f"{int(float(value))}")])
    runes_message_y_slider.set(runes_message_y)
    runes_message_y_slider.pack()

    # Toggle buttons for rune-related messages
    rune_messages_var = tk.BooleanVar(value=show_rune_messages)
    rune_messages_checkbox = ttk.Checkbutton(runes_tab, text="Show Rune Messages", variable=rune_messages_var, command=toggle_rune_messages)
    rune_messages_checkbox.pack()

    runic_obstruction_var = tk.BooleanVar(value=show_runic_obstruction)
    runic_obstruction_checkbox = ttk.Checkbutton(runes_tab, text="Show Runic Obstruction Messages", variable=runic_obstruction_var, command=toggle_runic_obstruction)
    runic_obstruction_checkbox.pack()

    anc_helm_proc_var = tk.BooleanVar(value=show_anc_helm_proc)
    anc_helm_proc_checkbox = ttk.Checkbutton(runes_tab, text="Show Anc Helm Proc Messages", variable=anc_helm_proc_var, command=toggle_anc_helm_proc)
    anc_helm_proc_checkbox.pack()

    dasher_message_var = tk.BooleanVar(value=show_dasher_message)
    dasher_message_checkbox = ttk.Checkbutton(runes_tab, text="Show Dasher Messages", variable=dasher_message_var, command=toggle_dasher_message)
    dasher_message_checkbox.pack()

    static_disarm_message_var = tk.BooleanVar(value=show_static_disarm_message)
    static_disarm_message_checkbox = ttk.Checkbutton(runes_tab, text="Show Static Disarm Messages", variable=static_disarm_message_var, command=toggle_static_disarm_message)
    static_disarm_message_checkbox.pack()

    honorable_blade_message_var = tk.BooleanVar(value=show_honorable_blade_message)
    honorable_blade_message_checkbox = ttk.Checkbutton(runes_tab, text="Show Imp Sword Messages", variable=honorable_blade_message_var, command=toggle_honorable_blade_message)
    honorable_blade_message_checkbox.pack()

    # Add "Show Test Text" toggle button for Runes tab
    runes_test_button = ttk.Button(runes_tab, text="Toggle Test Text", command=toggle_test_message)
    runes_test_button.pack()

    # ======= Chat Games Tab ========
    chat_games_tab = ttk.Frame(notebook)
    notebook.add(chat_games_tab, text="Chat Games")

    # Chat Games X and Y Position controls
    chat_game_x_label = ttk.Label(chat_games_tab, text="Chat Game X Position:")
    chat_game_x_label.pack()

    chat_game_x_value = tk.Label(chat_games_tab, text=f"{chat_game_x}")
    chat_game_x_value.pack()

    chat_game_x_slider = ttk.Scale(chat_games_tab, from_=0, to=500, orient='horizontal', length=400,
                                command=lambda value: [update_chat_game_x(value), chat_game_x_value.config(text=f"{int(float(value))}")])
    chat_game_x_slider.set(chat_game_x)
    chat_game_x_slider.pack()

    chat_game_y_label = ttk.Label(chat_games_tab, text="Chat Game Y Position:")
    chat_game_y_label.pack()

    chat_game_y_value = tk.Label(chat_games_tab, text=f"{chat_game_y}")
    chat_game_y_value.pack()

    chat_game_y_slider = ttk.Scale(chat_games_tab, from_=0, to=200, orient='horizontal', length=400,
                                command=lambda value: [update_chat_game_y(value), chat_game_y_value.config(text=f"{int(float(value))}")])
    chat_game_y_slider.set(chat_game_y)
    chat_game_y_slider.pack()

    # Toggle button for showing Chat Game messages
    chat_game_messages_var = tk.BooleanVar(value=show_chat_game_message)
    chat_game_messages_checkbox = ttk.Checkbutton(chat_games_tab, text="Show Chat Game Messages", variable=chat_game_messages_var, command=toggle_chat_game_message)
    chat_game_messages_checkbox.pack()

    # Add "Show Test Text" toggle button for Chat Games tab
    chat_games_test_button = ttk.Button(chat_games_tab, text="Toggle Test Text", command=toggle_chat_game_test_message)
    chat_games_test_button.pack()

    root.mainloop()

def toggle_chat_game_test_message():
    global test_message_displayed, test_message_object

    if test_message_displayed:
        # Remove the test message if it's displayed
        if test_message_object and test_message_object in chat_game_text_objects:
            chat_game_text_objects.remove(test_message_object)
        if test_message_object:  # Only clear the text if the object exists
            test_message_object.string.set_value("")  # Clear the text
        test_message_displayed = False
    else:
        # Show the test message if it's not displayed
        test_message_object = display_chat_game_message("Test Chat Game message", 0x00ff00, 5)  # Green color with 5 seconds
        test_message_displayed = True

def update_chat_game_x(value):
    global chat_game_x
    chat_game_x = int(float(value))
    save_config()

def update_chat_game_y(value):
    global chat_game_y
    chat_game_y = int(float(value))
    save_config()

# Function to run the GUI in a separate thread
def run_gui():
    """Run the GUI in a separate thread."""
    gui_thread = threading.Thread(target=create_gui)
    gui_thread.daemon = True
    gui_thread.start()

# Log message when the script starts (with blue color in-game)
def log_initialization():
    minescript.echo("§9Cerv's Utils has been initialized")  # In-game message in blue

# Main function
def main():
    """Continuously monitor chat for updates, update the countdown, and display the filtered messages."""
    log_initialization()  # Log the initialization message
    read_config()  # Read config at startup
    run_gui()  # Start the GUI in a separate thread

    # Display the "Waiting for a chat game to start" message at startup
    global current_chat_game_message
    current_chat_game_message = "Waiting for a chat game to start"
    display_chat_game_message(current_chat_game_message, 0xffd700, 0)

    with EventQueue() as event_queue:
        event_queue.register_chat_listener()
        while True:
            try:
                event = event_queue.get(timeout=0.1)
                if event and event.type == EventType.CHAT:
                    on_chat_message(event.message)
            except queue.Empty:
                pass
            except Exception as e:
                print(f"Error in event loop: {e}")

            update_runes_messages()  # Update rune messages countdown
            update_chat_game_messages()  # Update chat game messages countdown

if __name__ == "__main__":
    main()