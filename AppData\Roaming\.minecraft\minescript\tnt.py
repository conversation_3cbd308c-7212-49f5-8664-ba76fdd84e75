#!/usr/bin/env python3
"""
TNT Auto-Purchase Script for Minescript
=======================================

Automatically executes /shop tnt 1600 command at configurable intervals.
Features a modern CustomTkinter GUI with start/stop controls and speed adjustment.

Usage: \\tnt

Features:
- Automated TNT purchasing with configurable timing (0-10 seconds)
- Modern CustomTkinter GUI with start/stop controls
- Real-time speed adjustment with slider
- Purchase counter and statistics
- Emergency stop functionality

Author: Minescript Automation
Version: 1.0
"""

import time
import threading
import sys
import json
import os
from datetime import datetime

# Try to import CustomTkinter, install if not available
try:
    import customtkinter as ctk
except ImportError:
    import subprocess
    
    print("CustomTkinter not found. Installing...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "customtkinter"])
    import customtkinter as ctk

# Import Minescript module
try:
    import minescript
except ImportError as e:
    print(f"Error importing minescript: {e}")
    sys.exit(1)

# Set CustomTkinter appearance
ctk.set_appearance_mode("System")
ctk.set_default_color_theme("blue")

# Global state
STATE = {
    "running": False,
    "purchase_thread": None,
    "purchases_made": 0,
    "start_time": None,
    "current_interval": 1.7,  # Default 1.7 seconds
    "gui": None,
    # Auto home switching feature
    "auto_home_enabled": False,
    "home_switch_count": 50,  # Default purchases before switching
    "current_home": "tnt1",  # Current active home (tnt1 or tnt2)
    "purchases_since_switch": 0,  # Counter for purchases since last home switch
}

def purchase_tnt():
    """Execute the TNT purchase command and handle auto home switching."""
    try:
        minescript.execute("/shop tnt 1600")
        STATE["purchases_made"] += 1
        STATE["purchases_since_switch"] += 1

        minescript.echo(f"§a[TNT] Purchase #{STATE['purchases_made']} completed")

        # Check if auto home switching is enabled and threshold reached
        if STATE["auto_home_enabled"] and STATE["purchases_since_switch"] >= STATE["home_switch_count"]:
            execute_auto_home_switch()

        return True
    except Exception as e:
        minescript.echo(f"§c[TNT] Error executing purchase command: {e}")
        return False

def purchase_loop():
    """Main purchase loop that runs in a separate thread."""
    minescript.echo("§a[TNT] Auto-purchase started")
    STATE["start_time"] = datetime.now()

    while STATE["running"]:
        if purchase_tnt():
            # Wait for the specified interval
            time.sleep(STATE["current_interval"])
        else:
            # If purchase failed, wait a bit longer before retrying
            time.sleep(2.0)

    minescript.echo("§c[TNT] Auto-purchase stopped")

def start_purchasing():
    """Start the TNT purchasing process."""
    if STATE["running"]:
        return

    STATE["running"] = True
    STATE["purchases_made"] = 0
    STATE["purchases_since_switch"] = 0  # Reset auto home switching counter
    STATE["purchase_thread"] = threading.Thread(target=purchase_loop, daemon=True)
    STATE["purchase_thread"].start()

def stop_purchasing():
    """Stop the TNT purchasing process."""
    STATE["running"] = False
    if STATE["purchase_thread"] and STATE["purchase_thread"].is_alive():
        STATE["purchase_thread"].join(timeout=2.0)

def load_settings():
    """Load settings from JSON file."""
    settings_file = "tnt_settings.json"
    try:
        if os.path.exists(settings_file):
            with open(settings_file, 'r') as f:
                settings = json.load(f)
                STATE["auto_home_enabled"] = settings.get("auto_home_enabled", False)
                STATE["home_switch_count"] = settings.get("home_switch_count", 50)
                STATE["current_home"] = settings.get("current_home", "tnt1")
    except Exception as e:
        print(f"Error loading settings: {e}")

def save_settings():
    """Save settings to JSON file."""
    settings_file = "tnt_settings.json"
    try:
        settings = {
            "auto_home_enabled": STATE["auto_home_enabled"],
            "home_switch_count": STATE["home_switch_count"],
            "current_home": STATE["current_home"]
        }
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=2)
    except Exception as e:
        print(f"Error saving settings: {e}")

def update_interval(value):
    """Update the purchase interval from slider."""
    STATE["current_interval"] = float(value)

def execute_auto_home_switch():
    """Execute automatic home switching between tnt1 and tnt2."""
    try:
        # Switch to the other home
        if STATE["current_home"] == "tnt1":
            minescript.execute("/home tnt2")
            STATE["current_home"] = "tnt2"
            minescript.echo(f"§b[TNT] Auto-switched to /home tnt2 after {STATE['purchases_since_switch']} purchases")
        else:
            minescript.execute("/home tnt1")
            STATE["current_home"] = "tnt1"
            minescript.echo(f"§b[TNT] Auto-switched to /home tnt1 after {STATE['purchases_since_switch']} purchases")

        # Reset the counter
        STATE["purchases_since_switch"] = 0

    except Exception as e:
        minescript.echo(f"§c[TNT] Error executing auto home switch: {e}")

class TNTControlGUI:
    """Modern GUI for TNT auto-purchase control."""
    
    def __init__(self):
        # Load settings before creating GUI
        load_settings()

        self.root = ctk.CTk()
        self.root.title("TNT Auto-Purchase Control")
        self.root.geometry("420x600")  # Increased height for new controls
        self.root.resizable(True, True)

        # Configure grid
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(0, weight=1)

        # Create scrollable frame
        self.scrollable_frame = ctk.CTkScrollableFrame(self.root)
        self.scrollable_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.scrollable_frame.grid_columnconfigure(0, weight=1)

        self.setup_ui()
        self.update_stats()
        
    def setup_ui(self):
        """Set up the user interface."""
        # Title
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="TNT Auto-Purchase",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=20, sticky="ew")

        # Status frame
        status_frame = ctk.CTkFrame(self.scrollable_frame)
        status_frame.grid(row=1, column=0, padx=20, pady=10, sticky="ew")
        status_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(status_frame, text="Status:").grid(row=0, column=0, padx=10, pady=5, sticky="w")
        self.status_label = ctk.CTkLabel(status_frame, text="Stopped", text_color="red")
        self.status_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")
        
        ctk.CTkLabel(status_frame, text="Purchases:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.purchases_label = ctk.CTkLabel(status_frame, text="0")
        self.purchases_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        ctk.CTkLabel(status_frame, text="Interval:").grid(row=2, column=0, padx=10, pady=5, sticky="w")
        self.interval_label = ctk.CTkLabel(status_frame, text="1.7s")
        self.interval_label.grid(row=2, column=1, padx=10, pady=5, sticky="w")
        
        # Speed control frame
        speed_frame = ctk.CTkFrame(self.scrollable_frame)
        speed_frame.grid(row=2, column=0, padx=20, pady=10, sticky="ew")
        
        ctk.CTkLabel(speed_frame, text="Purchase Interval (seconds):", 
                    font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        self.speed_slider = ctk.CTkSlider(
            speed_frame,
            from_=0.1,
            to=10.0,
            number_of_steps=99,
            command=self.on_speed_change
        )
        self.speed_slider.set(1.7)  # Default value
        self.speed_slider.grid(row=1, column=0, padx=10, pady=5, sticky="ew")
        speed_frame.grid_columnconfigure(0, weight=1)
        
        # Control buttons frame
        button_frame = ctk.CTkFrame(self.scrollable_frame)
        button_frame.grid(row=3, column=0, padx=20, pady=10, sticky="ew")
        button_frame.grid_columnconfigure((0, 1), weight=1)

        self.start_button = ctk.CTkButton(
            button_frame,
            text="Start",
            command=self.start_purchasing,
            fg_color="green",
            hover_color="darkgreen"
        )
        self.start_button.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        self.stop_button = ctk.CTkButton(
            button_frame,
            text="Stop",
            command=self.stop_purchasing,
            fg_color="red",
            hover_color="darkred"
        )
        self.stop_button.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        # Home command buttons frame
        home_frame = ctk.CTkFrame(self.scrollable_frame)
        home_frame.grid(row=4, column=0, padx=20, pady=10, sticky="ew")
        home_frame.grid_columnconfigure((0, 1), weight=1)

        ctk.CTkLabel(home_frame, text="Quick Commands:",
                    font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=10, pady=5)

        self.home_tnt1_button = ctk.CTkButton(
            home_frame,
            text="/home tnt1",
            command=self.execute_home_tnt1,
            fg_color="blue",
            hover_color="darkblue"
        )
        self.home_tnt1_button.grid(row=1, column=0, padx=10, pady=5, sticky="ew")

        self.home_tnt2_button = ctk.CTkButton(
            home_frame,
            text="/home tnt2",
            command=self.execute_home_tnt2,
            fg_color="blue",
            hover_color="darkblue"
        )
        self.home_tnt2_button.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        # Auto Home Switching frame
        auto_home_frame = ctk.CTkFrame(self.scrollable_frame)
        auto_home_frame.grid(row=5, column=0, padx=20, pady=10, sticky="ew")
        auto_home_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(auto_home_frame, text="Auto Home Switching:",
                    font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=3, padx=10, pady=5)

        # Enable/Disable toggle
        self.auto_home_toggle = ctk.CTkCheckBox(
            auto_home_frame,
            text="Enable Auto Home Switching",
            command=self.toggle_auto_home
        )
        self.auto_home_toggle.grid(row=1, column=0, columnspan=3, padx=10, pady=5, sticky="w")
        # Set initial state from loaded settings
        if STATE["auto_home_enabled"]:
            self.auto_home_toggle.select()

        # Purchase count setting
        ctk.CTkLabel(auto_home_frame, text="Purchases before switching:").grid(row=2, column=0, padx=10, pady=5, sticky="w")

        self.home_switch_entry = ctk.CTkEntry(
            auto_home_frame,
            width=80,
            placeholder_text="50"
        )
        self.home_switch_entry.grid(row=2, column=1, padx=5, pady=5, sticky="w")
        self.home_switch_entry.insert(0, str(STATE["home_switch_count"]))
        self.home_switch_entry.bind("<KeyRelease>", self.update_home_switch_count)

        # Status displays
        ctk.CTkLabel(auto_home_frame, text="Current home:").grid(row=3, column=0, padx=10, pady=5, sticky="w")
        self.current_home_label = ctk.CTkLabel(auto_home_frame, text=f"/home {STATE['current_home']}")
        self.current_home_label.grid(row=3, column=1, padx=5, pady=5, sticky="w")

        ctk.CTkLabel(auto_home_frame, text="Next switch in:").grid(row=4, column=0, padx=10, pady=5, sticky="w")
        self.next_switch_label = ctk.CTkLabel(auto_home_frame, text="0 purchases")
        self.next_switch_label.grid(row=4, column=1, padx=5, pady=5, sticky="w")
        
    def on_speed_change(self, value):
        """Handle speed slider changes."""
        update_interval(value)
        self.interval_label.configure(text=f"{float(value):.1f}s")
        
    def start_purchasing(self):
        """Handle start button click."""
        start_purchasing()
        self.update_button_states()
        
    def stop_purchasing(self):
        """Handle stop button click."""
        stop_purchasing()
        self.update_button_states()

    def execute_home_tnt1(self):
        """Execute /home tnt1 command."""
        try:
            minescript.execute("/home tnt1")
            STATE["current_home"] = "tnt1"  # Update current home state
            minescript.echo("§a[TNT] Executed /home tnt1")
        except Exception as e:
            minescript.echo(f"§c[TNT] Error executing /home tnt1: {e}")

    def execute_home_tnt2(self):
        """Execute /home tnt2 command."""
        try:
            minescript.execute("/home tnt2")
            STATE["current_home"] = "tnt2"  # Update current home state
            minescript.echo("§a[TNT] Executed /home tnt2")
        except Exception as e:
            minescript.echo(f"§c[TNT] Error executing /home tnt2: {e}")

    def toggle_auto_home(self):
        """Toggle auto home switching on/off."""
        STATE["auto_home_enabled"] = self.auto_home_toggle.get()
        save_settings()  # Save settings when changed
        if STATE["auto_home_enabled"]:
            minescript.echo("§a[TNT] Auto home switching enabled")
        else:
            minescript.echo("§c[TNT] Auto home switching disabled")

    def update_home_switch_count(self, event=None):
        """Update the home switch count from entry field."""
        try:
            value = int(self.home_switch_entry.get())
            if 1 <= value <= 500:
                STATE["home_switch_count"] = value
                save_settings()  # Save settings when changed
            else:
                # Reset to valid range if out of bounds
                if value < 1:
                    STATE["home_switch_count"] = 1
                else:
                    STATE["home_switch_count"] = 500
                self.home_switch_entry.delete(0, ctk.END)
                self.home_switch_entry.insert(0, str(STATE["home_switch_count"]))
                save_settings()
        except ValueError:
            # Reset to default if invalid input
            STATE["home_switch_count"] = 50
            self.home_switch_entry.delete(0, ctk.END)
            self.home_switch_entry.insert(0, "50")
            save_settings()
        
    def update_button_states(self):
        """Update button states based on current status."""
        if STATE["running"]:
            self.start_button.configure(state="disabled")
            self.stop_button.configure(state="normal")
            self.status_label.configure(text="Running", text_color="green")
        else:
            self.start_button.configure(state="normal")
            self.stop_button.configure(state="disabled")
            self.status_label.configure(text="Stopped", text_color="red")
            
    def update_stats(self):
        """Update statistics display."""
        self.purchases_label.configure(text=str(STATE["purchases_made"]))
        self.update_button_states()

        # Update auto home switching status
        self.current_home_label.configure(text=f"/home {STATE['current_home']}")

        if STATE["auto_home_enabled"]:
            remaining = STATE["home_switch_count"] - STATE["purchases_since_switch"]
            self.next_switch_label.configure(text=f"{remaining} purchases")
        else:
            self.next_switch_label.configure(text="Disabled")

        # Schedule next update
        self.root.after(500, self.update_stats)
        
    def run(self):
        """Start the GUI main loop."""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.cleanup()
            
    def cleanup(self):
        """Clean up resources when closing."""
        stop_purchasing()
        save_settings()  # Save settings before closing
        self.root.quit()

def main():
    """Main function to start the TNT auto-purchase system."""
    minescript.echo("§a[TNT] Starting TNT Auto-Purchase System")

    # Create and run GUI
    STATE["gui"] = TNTControlGUI()

    try:
        STATE["gui"].run()
    except Exception as e:
        minescript.echo(f"§c[TNT] Error running GUI: {e}")
    finally:
        # Cleanup
        stop_purchasing()
        minescript.echo("§c[TNT] TNT Auto-Purchase System stopped")

if __name__ == "__main__":
    main()
