import time
import re
import math
import minescript
from minescript import EventQueue, EventType
from queue import Empty
from collections import deque

# Define your player name
my_name = "cerv"

# Get the list of nearby players
player_list = minescript.players()

# Get your own position - handle case where no local player is found
try:
    my_position = next(player.position for player in player_list if player.local)
except StopIteration:
    minescript.echo("§cError: Could not find local player position")
    exit()

# Filter out the local player and names with exactly three digits
nearby_players = [
    player for player in player_list
    if not player.local and not re.search(r'.*\d{3}.*', player.name)
]

# Check if there are any nearby players to process
if not nearby_players:
    minescript.echo("§eNo nearby players found to check")
    exit()

# Helper function to determine direction based on coordinate differences
def get_direction(target_position, my_position):
    dx = target_position[0] - my_position[0]
    dz = target_position[2] - my_position[2]
    
    if abs(dx) > abs(dz):
        if dx > 0:
            primary = "east"
        else:
            primary = "west"
    else:
        if dz > 0:
            primary = "south"
        else:
            primary = "north"
    
    # Combine primary and secondary directions for diagonal guidance
    if abs(dx) > 0.5 and abs(dz) > 0.5:
        if dx > 0 and dz > 0:
            return "southeast"
        elif dx > 0 and dz < 0:
            return "northeast"
        elif dx < 0 and dz > 0:
            return "southwest"
        elif dx < 0 and dz < 0:
            return "northwest"
    return primary

# Helper function to parse minutes from time_online string
def get_minutes(time_online):
    minutes = 0
    match = re.search(r"(\d+)\s*minutes?", time_online)
    if match:
        minutes += int(match.group(1))
    match = re.search(r"(\d+)\s*hours?", time_online)
    if match:
        minutes += int(match.group(1)) * 60
    return minutes

# Initialize player data dictionary and pending queue
player_data = {}
pending_players = deque()  # Queue to track players in order of /seen commands

# Start the event queue and chat message listener before issuing /seen commands
with EventQueue() as event_queue:
    event_queue.register_chat_listener()

    # Initialize player data and add each player to the pending queue
    for player in nearby_players:
        name = player.name
        rounded_coords = [round(coord) for coord in player.position]
        
        # Calculate distance from you (Euclidean distance)
        distance = math.sqrt(
            (player.position[0] - my_position[0]) ** 2 +
            (player.position[1] - my_position[1]) ** 2 +
            (player.position[2] - my_position[2]) ** 2
        )
        rounded_distance = round(distance)

        # Determine direction
        direction = get_direction(player.position, my_position)

        # Store initial data for this player
        player_data[name] = {
            "coords": rounded_coords,
            "distance": rounded_distance,
            "direction": direction,
            "time_online": "Unknown"
        }

        # Add player to the pending queue
        pending_players.append(name)

    # Issue /seen command for each player with a delay
    for player in nearby_players:
        name = player.name
        minescript.execute(f"/seen {name}")
        time.sleep(0.3)  # Slightly longer delay to ensure each command is processed

    # Continuously check for chat messages and associate them with players in order
    max_wait_time = 2  # Maximum time to wait for all messages, in seconds
    start_time = time.time()

    while time.time() - start_time < max_wait_time and pending_players:
        try:
            event = event_queue.get(timeout=0.3)  # Wait up to 0.3 seconds per message
            if event and event.type == EventType.CHAT:
                chat_message = event.message

                # Fully flexible regex pattern to capture rank, name, and time with `.*`
                match = re.search(r".* \[(.*)\] (.*) has been online on Skyblock for (.*)\.", chat_message)
                if match:
                    # Extract the time_online from the matched message
                    time_online = match.group(3)

                    # Get the next player in line for a match from the pending queue
                    original_name = pending_players.popleft()

                    # Update player data with time
                    player_data[original_name]["time_online"] = time_online
        except Empty:
            # No event was available; continue to the next attempt
            continue

# Display all the collected information after all /seen commands and messages are processed
for name, data in player_data.items():
    coords = data["coords"]
    distance = data["distance"]
    direction = data["direction"]
    time_online = data["time_online"]

    # Determine color and boldness based on time_online
    minutes = get_minutes(time_online)
    if minutes >= 10:
        time_display = f"§a§l{time_online}"  # Green and bold if >= 10 minutes
    else:
        time_display = f"§c{time_online}"  # Red and not bold if < 10 minutes

    # Custom output format with color
    output = (
        f"§c{name} = §b[{coords[0]}, {coords[1]}, {coords[2]}] | "
        f"§b{distance}m away | direction: §b{direction} | "
        f"In tomb for {time_display}"
    )
    minescript.echo(output)
