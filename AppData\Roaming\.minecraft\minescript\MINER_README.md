# Automated Mining Bot (miner.py)

## Overview
An advanced Minescript automation script that moves the player in a clockwise square pattern while continuously mining. Features a modern CustomTkinter GUI with real-time statistics and controls.

## Features
- **Automated Square Pattern Mining**: Moves in a clockwise pattern within a defined area (-39,-39 to 40,40)
- **Continuous Left-Click Mining**: Maintains constant attack/mining action
- **Real-time Position Tracking**: Shows current coordinates and movement direction
- **Modern GUI Interface**: CustomTkinter-based interface with intuitive controls
- **Safety Features**: Emergency stop and automatic boundary checking
- **Configurable Settings**: Adjustable movement speed and timing

## Usage

### Starting the Script
1. In Minecraft, open the chat (press `T` or `/`)
2. Type: `\miner`
3. Press Enter to launch the GUI

### GUI Controls

#### Main Controls
- **Start Mining**: Begin the automated mining process
- **Stop Mining**: Stop the mining and return control to player
- **Pause/Resume**: Temporarily pause mining without stopping
- **Emergency Stop**: Immediately stop all movements and mining

#### Statistics Display
- **Status**: Current state (Running/Paused/Stopped/Emergency Stop)
- **Runtime**: How long the script has been running
- **Position**: Current player coordinates (X, Y, Z)
- **Direction**: Current movement direction
- **Mining Area**: Configured mining boundaries

#### Settings
- **Movement Speed**: Slider to adjust how fast the bot moves/checks position
- **Return to Start**: Shows starting position (manual navigation required)

## Movement Pattern

The bot follows a clockwise square pattern:

```
(-39,40) ←←←←←←←←← (40,40)
    ↓                 ↑
    ↓                 ↑
    ↓                 ↑
    ↓                 ↑
    ↓                 ↑
    ↓                 ↑
    ↓                 ↑
(-39,-39) →→→→→→→→→ (40,-39)
```

### Direction Logic
- **Bottom Edge** (-39,-39) to (40,-39): Move Forward + Right
- **Right Edge** (40,-39) to (40,40): Move Forward + Left
- **Top Edge** (40,40) to (-39,40): Move Backward + Left
- **Left Edge** (-39,40) to (-39,-39): Move Backward + Right

## Safety Features

### Automatic Safety Checks
- **Boundary Detection**: Stops if player moves outside safe area (with 5-block margin)
- **Emergency Stop**: Immediately halts all movement and mining
- **Thread Safety**: Proper cleanup when stopping the script

### Manual Safety Controls
- **Emergency Stop Button**: Red button for immediate halt
- **Pause Function**: Temporary stop without ending the session
- **Position Monitoring**: Real-time coordinate display

## Configuration

### Default Settings
```python
CONFIG = {
    "mining_area": {
        "min_x": -39,
        "min_z": -39,
        "max_x": 40,
        "max_z": 40
    },
    "movement_speed": 0.1,
    "position_check_interval": 0.05,
    "gui_update_interval": 0.5,
    "safety_margin": 5,
}
```

### Customizing the Mining Area
To change the mining area, edit the `CONFIG` dictionary at the top of the script:
- `min_x`, `min_z`: Bottom-left corner coordinates
- `max_x`, `max_z`: Top-right corner coordinates
- `safety_margin`: Extra blocks outside area before emergency stop

## Requirements

### Dependencies
- **Python 3.7+**: Required for the script
- **CustomTkinter**: Automatically installed if not present
- **Minescript Mod**: Must be installed in Minecraft

### Minecraft Setup
1. Install the Minescript mod for your Minecraft version
2. Ensure Python is configured in `minescript/config.txt`
3. Place `miner.py` in your `minescript` folder

## Troubleshooting

### Common Issues

#### Script Won't Start
- Check that Minescript mod is properly installed
- Verify Python path in `minescript/config.txt`
- Ensure `miner.py` is in the correct directory

#### Movement Issues
- Check that player has sufficient space to move
- Verify the mining area coordinates are accessible
- Ensure no blocks are obstructing movement

#### GUI Issues
- CustomTkinter will auto-install if missing
- Close and restart if GUI becomes unresponsive
- Use Emergency Stop if controls become unresponsive

### Error Messages
- **"Outside safe area"**: Player moved beyond configured boundaries
- **"Error getting position"**: Temporary connection issue with Minecraft
- **"Mining stopped"**: Normal shutdown or error occurred

## Advanced Usage

### Modifying Movement Speed
Use the GUI slider or edit `CONFIG["movement_speed"]`:
- Lower values (0.05): Faster movement, more CPU usage
- Higher values (0.5): Slower movement, less CPU usage

### Custom Mining Patterns
To implement different patterns, modify the `get_mining_direction()` function to return different movement combinations based on position.

## Safety Warnings

⚠️ **Important Safety Notes**:
- Always monitor the bot while running
- Use in safe, enclosed areas to prevent falling
- Keep the Emergency Stop button accessible
- Test in creative mode before using in survival
- Be aware of server rules regarding automation

## Support

For issues or questions:
1. Check the Minescript documentation
2. Verify your setup matches the requirements
3. Test with default settings first
4. Use Emergency Stop if anything goes wrong

---

**Version**: 1.0  
**Author**: Minescript Automation  
**Compatible**: Minescript v4.0+
