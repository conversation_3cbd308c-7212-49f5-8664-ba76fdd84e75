import time
import threading
import minescript

def hold_attack():
    """Hold down the attack button to break blocks."""
    minescript.player_press_attack(True)  # Start holding attack

def check_inventory_and_sell():
    """Continuously check for any item in slot 27 in the inventory. If found, execute /sell all with a 2-second delay."""
    while True:
        inventory = minescript.player_inventory()  # Get the player's inventory

        # Check if any item in the inventory has slot=27
        sell_trigger = any("slot=27" in str(item) for item in inventory)

        if sell_trigger:
            minescript.execute("/sell all")  # Execute the sell command
            time.sleep(2)  # Wait 2 seconds before checking again
        else:
            time.sleep(1)  # Wait 1 second before checking again if slot 27 is empty

def normalize_yaw(yaw):
    """Normalize yaw to the range of -180 to 180 degrees."""
    while yaw > 180:
        yaw -= 360
    while yaw < -180:
        yaw += 360
    return yaw

def move_down_one_layer():
    """Move down one block to the next layer."""
    minescript.player_press_sneak(True)
    time.sleep(0.2)  # Small delay for sneak activation
    minescript.player_press_sneak(False)
    time.sleep(0.25)  # Adjusted time for a 1-block drop

def set_closest_east_or_west():
    # Get current yaw and pitch
    yaw, pitch = minescript.player_orientation()
    yaw = normalize_yaw(yaw)  # Normalize yaw to -180 to 180

    # Determine the closest direction (either East or West) with pitch set to 20
    if -135 <= yaw < -45:
        # Closest to East
        minescript.player_set_orientation(-90, 20)
        return "east"
    elif 45 <= yaw < 135:
        # Closest to West
        minescript.player_set_orientation(90, 20)
        return "west"
    else:
        # Default to East if no clear choice
        minescript.player_set_orientation(-90, 20)
        return "east"

def check_and_move_down_if_fully_moist_farmland(facing):
    """Check if the player is looking at fully moist farmland (moisture=7) from the correct side based on facing direction, and move down slightly until not."""
    
    # Determine the target side based on facing direction
    target_side = "west" if facing == "east" else "east"
    
    while True:
        # Get the block the player is looking at
        looking_at_block = minescript.player_get_targeted_block()

        # Check if the block type is fully moist farmland and the side matches the target side
        if looking_at_block.type == "minecraft:farmland[moisture=7]" and looking_at_block.side == target_side:
            minescript.player_press_sneak(True)
            time.sleep(0.1)  # Move down for 0.1 seconds
            minescript.player_press_sneak(False)
            time.sleep(0.1)  # Cooldown of 0.1 seconds
        else:
            break  # Exit loop if no longer looking at fully moist farmland on the correct side

def first_layer(facing):
    """Move in the correct direction for the first layer based on facing direction."""
    set_closest_east_or_west()
    #minescript.player_press_forward(True)  # Start holding forward
    while True:
        x, y, z = [int(coord) for coord in minescript.player_position()]

        if facing == "east":
            block_type = minescript.getblock(x + 1, y, z + 1)
        else:
            block_type = minescript.getblock(x - 1, y, z - 1)

        if block_type == "minecraft:air":
            minescript.log("Detected air block relative to player. Stopping movement.")
            break

        # Check if looking at fully moist farmland and move down if necessary
        check_and_move_down_if_fully_moist_farmland(facing)

        if facing == "east":
            minescript.player_press_right(True)
        else:
            minescript.player_press_right(True)  # Moving right when facing west
        
        time.sleep(0.1)
        time.sleep(1)

    # Stop pressing the keys after the loop ends
    minescript.player_press_right(False)
    #minescript.player_press_forward(False)
    move_down_one_layer()

def second_layer(facing):
    """Move in the correct direction for the second layer based on facing direction."""
    #minescript.player_press_forward(True)  # Start holding forward
    while True:
        x, y, z = [int(coord) for coord in minescript.player_position()]

        if facing == "east":
            block_type = minescript.getblock(x + 1, y, z - 2)
        else:
            block_type = minescript.getblock(x - 1, y, z + 1)

        if block_type == "minecraft:air":
            minescript.log("Detected air block relative to player. Stopping movement.")
            break

        # Check if looking at fully moist farmland and move down if necessary
        check_and_move_down_if_fully_moist_farmland(facing)

        if facing == "east":
            minescript.player_press_left(True)
        else:
            minescript.player_press_left(True)
        
        time.sleep(0.1)
        time.sleep(1)

    # Stop pressing the keys after the loop ends
    minescript.player_press_left(False)
    #minescript.player_press_forward(False)
    move_down_one_layer()

# Start the inventory check in a separate thread
inventory_thread = threading.Thread(target=check_inventory_and_sell)
inventory_thread.daemon = True  # Daemonize thread to exit when main program exits
inventory_thread.start()

# Start holding the attack key
hold_attack()

# Main loop to repeat both layers indefinitely
while True:
    facing_direction = set_closest_east_or_west()  # Determine initial facing direction
    first_layer(facing_direction)
    second_layer(facing_direction)
