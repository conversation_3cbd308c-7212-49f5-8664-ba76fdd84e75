import re
import time
import customtkinter as ctk
from threading import Thread
from minescript import EventQueue, EventType, chat, execute

class ChatApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Minecraft Chat")
        self.geometry("500x650")

        # Define font for the app
        self.font = ("<PERSON><PERSON>ri", 14, "bold")  # Font: Calibri, Size: 14, Style: Bold

        # Recent messages bar
        self.history_frame = ctk.CTkFrame(self, height=40, fg_color="black")  # Black border
        self.history_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        self.history_buttons = [ctk.CTkButton(self.history_frame, text="", font=self.font, command=None, width=150) for _ in range(3)]
        for i, button in enumerate(self.history_buttons):
            button.grid(row=0, column=i, padx=5, pady=5, sticky="ew")
        self.history = []

        # Chat display area
        self.chat_display = ctk.CTkTextbox(self, wrap="word", state="disabled", width=480, height=450, font=self.font)
        self.chat_display.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")

        # Message input area
        self.message_entry = ctk.CTkEntry(self, placeholder_text="Type your message here...", font=self.font)
        self.message_entry.grid(row=2, column=0, padx=10, pady=(0, 10), sticky="ew")
        self.message_entry.bind("<Return>", self.send_message)

        # Button to send the message
        self.send_button = ctk.CTkButton(self, text="Send", font=self.font, command=self.send_message)
        self.send_button.grid(row=3, column=0, padx=10, pady=10, sticky="ew")

        # Adjust layout
        self.grid_rowconfigure(1, weight=1)
        self.grid_columnconfigure(0, weight=1)

    def append_to_chat(self, message):
        """Append a new message to the chat display."""
        self.chat_display.configure(state="normal")
        self.chat_display.insert("end", message + "\n")
        self.chat_display.configure(state="disabled")
        self.chat_display.see("end")

    def send_message(self, event=None):
        """Send a chat message."""
        message = self.message_entry.get()
        if message:
            # Handle commands and regular messages
            if message.startswith("/"):
                execute(message)  # Use execute for commands
            else:
                chat(message)  # Use chat for regular messages

            # Update history bar
            self.update_history(message)
            self.message_entry.delete(0, "end")  # Clear the input field

    def update_history(self, message):
        """Update the top bar with the last three messages."""
        self.history.insert(0, message)
        self.history = self.history[:3]  # Keep only the last 3 messages
        for i, msg in enumerate(self.history):
            self.history_buttons[i].configure(text=msg, command=lambda m=msg: self.resend_message(m))

    def resend_message(self, message):
        """Resend a message from the history."""
        if message.startswith("/"):
            execute(message)
        else:
            chat(message)

# Function to monitor and process chat events
def monitor_chat():
    """Continuously monitor chat for updates and process messages."""
    with EventQueue() as event_queue:
        event_queue.register_chat_listener()
        while True:
            try:
                # Get the next event
                event = event_queue.get()

                # Process chat events
                if event.type == EventType.CHAT:
                    on_chat_message(event.message)
            except Exception as e:
                print(f"Error while processing event: {e}")
            # Sleep briefly to avoid excessive processing
            time.sleep(0.1)

# Handle received chat messages
def on_chat_message(message):
    """Handle received chat messages."""
    # Remove emojis and special characters using regex
    message_without_emojis = re.sub(r'[^\x00-\x7F]+', '', message)
    app.append_to_chat(message_without_emojis)

# Start the GUI and chat listener
if __name__ == "__main__":
    app = ChatApp()
    # Start the chat monitor in a separate thread
    chat_thread = Thread(target=monitor_chat, daemon=True)
    chat_thread.start()
    app.mainloop()
